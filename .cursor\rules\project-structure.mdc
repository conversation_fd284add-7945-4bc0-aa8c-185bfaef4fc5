---
description: 
globs: 
alwaysApply: false
---
# 项目结构说明

## 核心目录结构
```
src/
├── components/           # 公共组件
│   ├── AppHeader.vue    # 页头组件 - 包含导航和返回按钮
│   └── AppFooter.vue    # 页脚组件 - 版权信息
├── views/               # 页面组件 (4个主要页面)
│   ├── RoleSelectionView.vue    # 身份选择页面
│   ├── StudentSelectionView.vue # 学生选择页面  
│   ├── EvaluationView.vue       # 评价打分页面
│   └── ScoreView.vue            # 成绩展示页面
├── stores/              # Pinia 状态管理
│   └── evaluation.ts    # 评价数据状态管理
├── types/               # TypeScript 类型定义
│   └── index.ts         # 全局类型定义
├── constants/           # 常量数据
│   └── data.ts          # 学生数据和评价维度配置
├── assets/styles/       # 样式文件
│   ├── main.scss        # 主样式文件
│   └── tailwind.css     # Tailwind CSS 自定义样式
└── router/              # Vue Router 配置
    └── index.ts         # 路由配置
```

## 关键文件说明

### 主要页面组件
- [RoleSelectionView.vue](mdc:src/views/RoleSelectionView.vue) - 首页，选择评价身份
- [StudentSelectionView.vue](mdc:src/views/StudentSelectionView.vue) - 学生列表和搜索
- [EvaluationView.vue](mdc:src/views/EvaluationView.vue) - 评分界面，五维度评价
- [ScoreView.vue](mdc:src/views/ScoreView.vue) - 成绩展示，包含多种图表

### 数据和状态管理
- [src/stores/evaluation.ts](mdc:src/stores/evaluation.ts) - Pinia store，管理评价数据
- [src/constants/data.ts](mdc:src/constants/data.ts) - 46个学生数据和5个评价维度
- [src/types/index.ts](mdc:src/types/index.ts) - 完整的TypeScript类型定义

### 配置文件
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind CSS 配置，包含项目色彩系统
- [src/router/index.ts](mdc:src/router/index.ts) - 路由配置，4个主要页面路由

## 页面流程
1. 身份选择 (`/`) → 2. 学生选择 (`/student-selection`) → 3. 评价打分 (`/evaluation`) → 4. 成绩展示 (`/score`)

