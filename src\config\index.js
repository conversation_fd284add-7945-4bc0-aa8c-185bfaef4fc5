/**
 * 应用配置管理
 * 统一管理环境变量和应用配置
 */

/**
 * 应用配置类型
 * @typedef {Object} AppConfig
 * @property {Object} app - 应用配置
 * @property {string} app.title - 应用标题
 * @property {string} app.version - 应用版本
 * @property {boolean} app.debug - 调试模式
 * @property {'debug' | 'info' | 'warn' | 'error'} app.logLevel - 日志级别
 * @property {Object} api - API配置
 * @property {string} api.baseURL - API基础URL
 * @property {number} api.timeout - 请求超时时间
 * @property {Object} oauth - OAuth配置
 * @property {string[]} oauth.providers - OAuth提供商
 * @property {boolean} oauth.enabled - 是否启用OAuth
 * @property {Object} features - 功能配置
 * @property {boolean} features.sso - 是否启用SSO
 * @property {boolean} features.oauth - 是否启用OAuth
 * @property {boolean} features.registration - 是否启用注册
 */

/**
 * 获取环境变量
 * @param {string} key - 环境变量键
 * @param {string} [defaultValue] - 默认值
 * @returns {string} 环境变量值
 */
const getEnv = (key, defaultValue) => {
  return import.meta.env[key] || defaultValue || ''
}

/**
 * 获取布尔类型环境变量
 * @param {string} key - 环境变量键
 * @param {boolean} [defaultValue=false] - 默认值
 * @returns {boolean} 布尔值
 */
const getBoolEnv = (key, defaultValue = false) => {
  const value = getEnv(key)
  if (!value) return defaultValue
  const lowerValue = value.toLowerCase()
  return lowerValue === 'true' || lowerValue === '1'
}

/**
 * 获取数组类型环境变量
 * @param {string} key - 环境变量键
 * @param {string[]} [defaultValue=[]] - 默认值
 * @returns {string[]} 字符串数组
 */
const getArrayEnv = (key, defaultValue = []) => {
  const value = getEnv(key)
  return value ? value.split(',').map((item) => item.trim()) : defaultValue
}

/** @type {AppConfig} */
export const config = {
  app: {
    title: getEnv('VITE_APP_TITLE', 'Hydroline Services'),
    version: getEnv('VITE_APP_VERSION', '1.0.0'),
    debug: getBoolEnv('VITE_DEBUG', false),
    logLevel: /** @type {'debug' | 'info' | 'warn' | 'error'} */ (
      getEnv('VITE_LOG_LEVEL', 'info')
    ),
  },
  api: {
    baseURL: getEnv('VITE_API_URL', 'http://localhost:3000'),
    timeout: parseInt(getEnv('VITE_API_TIMEOUT', '10000'), 10),
  },
  oauth: {
    providers: getArrayEnv('VITE_OAUTH_PROVIDERS', ['microsoft']),
    enabled: getBoolEnv('VITE_ENABLE_OAUTH', true),
  },
  features: {
    sso: getBoolEnv('VITE_ENABLE_SSO', true),
    oauth: getBoolEnv('VITE_ENABLE_OAUTH', true),
    registration: getBoolEnv('VITE_ENABLE_REGISTRATION', true),
  },
}

/**
 * 配置验证
 * @returns {boolean} 配置是否有效
 */
export const validateConfig = () => {
  /** @type {string[]} */
  const errors = []

  if (!config.api.baseURL) {
    errors.push('API_URL is required')
  }

  if (config.api.timeout <= 0) {
    errors.push('API_TIMEOUT must be greater than 0')
  }

  if (errors.length > 0) {
    console.error('Configuration errors:', errors)
    return false
  }

  return true
}

// 开发环境配置检查
if (config.app.debug) {
  console.group('🔧 Application Configuration')
  console.table(config)
  console.groupEnd()

  if (!validateConfig()) {
    throw new Error('Invalid configuration detected')
  }
}

export default config
