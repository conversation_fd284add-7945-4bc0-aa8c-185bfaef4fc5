# 组件模块 (Components)

该目录包含项目的所有可复用Vue组件，采用分层架构设计，支持组件的模块化组织和复用。

## 📁 文件结构

```
src/components/
├── AppHeader.vue         # 应用头部组件
├── AppFooter.vue         # 应用底部组件
├── auth/                 # 认证相关组件
│   ├── LoginDialog.vue   # 登录对话框
│   ├── index.js          # 认证组件导出
│   └── README.md         # 认证组件文档
└── layouts/              # 布局组件
    ├── PageHeader.vue    # 页面头部
    ├── PageFooter.vue    # 页面底部
    ├── PageContent.vue   # 页面内容区
    └── PageSidebar.vue   # 页面侧边栏
```

## 🏗️ 组件架构

### 1. 组件分层
```
应用级组件 (App*)
    ↓
布局组件 (layouts/)
    ↓
功能组件 (auth/, ui/)
    ↓
基础组件 (ui/base/)
```

### 2. 组件类型
- **应用级组件**: 全局唯一的顶层组件（AppHeader、AppFooter）
- **布局组件**: 页面结构和布局相关组件
- **功能组件**: 特定业务功能的组件模块
- **基础组件**: 可复用的UI基础组件

## 📄 组件详解

### 应用级组件

#### `AppHeader.vue` - 应用头部
**功能**: 全局应用头部，包含标题、导航和用户信息

**特性**:
- 响应式设计，适配移动端和桌面端
- 集成用户认证状态显示
- 支持用户下拉菜单（个人中心、登出）
- 包含飞机图标动画效果

**使用场景**: 在根组件 `App.vue` 中使用

<augment_code_snippet path="src/components/AppHeader.vue" mode="EXCERPT">
````vue
<template>
  <header class="bg-primary text-white shadow-md">
    <div class="container mx-auto px-4">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <i class="fa fa-plane text-2xl mr-3 airplane-animation"></i>
          <h1 class="text-xl md:text-2xl font-bold !mb-0">
            客舱设施与服务五力评价系统
          </h1>
        </div>
````
</augment_code_snippet>

**依赖**:
- `@/stores/auth` - 用户认证状态
- Ant Design Vue - 下拉菜单组件
- Font Awesome - 图标库

#### `AppFooter.vue` - 应用底部
**功能**: 全局应用底部，显示版权信息

**特性**:
- 简洁的版权信息展示
- 响应式设计
- 自动粘贴到页面底部

<augment_code_snippet path="src/components/AppFooter.vue" mode="EXCERPT">
````vue
<template>
  <footer class="bg-primary text-white py-4 mt-auto">
    <div class="container mx-auto px-4 text-center text-sm">
      <p>© 2025 客舱设施与服务五力评价系统 | 航空职业教育评价平台</p>
    </div>
  </footer>
</template>
````
</augment_code_snippet>

### 功能组件模块

#### `auth/` - 认证组件模块
**功能**: 处理用户认证相关的所有UI组件

**组件列表**:
- `LoginDialog.vue` - 登录对话框组件
- `index.js` - 统一导出文件

**详细文档**: [auth/README.md](./auth/README.md)

**使用示例**:
```javascript
import { LoginDialog } from '@/components/auth'

// 在组件中使用
<LoginDialog v-model:visible="showLogin" @success="handleLoginSuccess" />
```

#### `layouts/` - 布局组件模块
**功能**: 提供页面布局相关的组件

**组件列表**:
- `PageHeader.vue` - 页面级头部组件
- `PageFooter.vue` - 页面级底部组件
- `PageContent.vue` - 页面内容容器
- `PageSidebar.vue` - 页面侧边栏

**设计特点**:
- 支持灵活的页面布局组合
- 响应式设计
- 统一的间距和样式规范

## 🚀 使用指南

### 1. 组件导入

**直接导入**:
```javascript
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
```

**模块导入**:
```javascript
import { LoginDialog } from '@/components/auth'
```

### 2. 在页面中使用

**基础布局**:
```vue
<template>
  <div class="min-h-screen flex flex-col">
    <AppHeader />
    
    <main class="flex-1">
      <!-- 页面内容 -->
      <router-view />
    </main>
    
    <AppFooter />
  </div>
</template>

<script setup>
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
</script>
```

**使用布局组件**:
```vue
<template>
  <div class="page-container">
    <PageHeader title="评价管理" />
    
    <PageContent>
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <PageSidebar class="lg:col-span-1">
          <!-- 侧边栏内容 -->
        </PageSidebar>
        
        <div class="lg:col-span-3">
          <!-- 主要内容 -->
        </div>
      </div>
    </PageContent>
    
    <PageFooter />
  </div>
</template>

<script setup>
import { PageHeader, PageContent, PageSidebar, PageFooter } from '@/components/layouts'
</script>
```

### 3. 组件通信

**Props传递**:
```vue
<template>
  <LoginDialog 
    :visible="showDialog"
    :loading="isLoading"
    @update:visible="showDialog = $event"
    @submit="handleLogin"
  />
</template>
```

**事件监听**:
```javascript
// 在父组件中
const handleLogin = (loginData) => {
  // 处理登录逻辑
}

const handleLoginSuccess = (userInfo) => {
  // 登录成功处理
  showDialog.value = false
}
```

## 🎨 样式规范

### 1. 样式架构
- **Tailwind CSS**: 优先使用原子化CSS类
- **Scoped样式**: 组件特定样式使用`<style scoped>`
- **全局样式**: 避免在组件中定义全局样式

### 2. 响应式设计
```vue
<template>
  <!-- 响应式容器 -->
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <!-- 响应式网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- 内容 -->
    </div>
  </div>
</template>
```

### 3. 主题一致性
```vue
<style scoped>
.custom-button {
  @apply px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark;
  @apply transition-colors duration-200;
}
</style>
```

## 🔧 开发规范

### 1. 组件命名
- **文件名**: PascalCase，如 `LoginDialog.vue`
- **组件名**: 与文件名一致
- **应用级组件**: 以 `App` 前缀开头
- **页面级组件**: 以 `Page` 前缀开头

### 2. 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 2. 定义Props和Emits
interface Props {
  visible: boolean
  title?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'submit', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '默认标题'
})

const emit = defineEmits<Emits>()

// 3. 响应式数据
const loading = ref(false)

// 4. 计算属性
const isDisabled = computed(() => loading.value)

// 5. 方法定义
const handleSubmit = () => {
  emit('submit', formData)
}

// 6. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/* 组件样式 */
</style>
```

### 3. Props定义
```typescript
interface Props {
  // 必需属性
  id: string
  
  // 可选属性
  title?: string
  
  // 带默认值
  size?: 'small' | 'medium' | 'large'
  
  // 复杂类型
  data?: Array<{ id: string; name: string }>
}

const props = withDefaults(defineProps<Props>(), {
  title: '默认标题',
  size: 'medium',
  data: () => []
})
```

### 4. 事件定义
```typescript
interface Emits {
  // 基础事件
  (e: 'click'): void
  
  // 带参数事件
  (e: 'change', value: string): void
  
  // v-model支持
  (e: 'update:modelValue', value: any): void
}

const emit = defineEmits<Emits>()
```

## 🔗 相关文件

- [`src/App.vue`](../App.vue) - 根组件，使用应用级组件
- [`src/views/`](../views/) - 页面组件，使用功能组件
- [`src/stores/`](../stores/) - 状态管理，组件数据源
- [`tailwind.config.js`](../../tailwind.config.js) - 样式配置

## 📝 最佳实践

### 1. 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 设计通用的、可配置的组件
- **可测试性**: 组件逻辑清晰，易于测试
- **可维护性**: 代码结构清晰，注释完整

### 2. 性能优化
- **按需导入**: 只导入需要的组件和功能
- **懒加载**: 大型组件使用动态导入
- **缓存优化**: 合理使用computed和watch
- **事件清理**: 在组件卸载时清理事件监听器

### 3. 可访问性
- **语义化HTML**: 使用正确的HTML标签
- **键盘导航**: 支持键盘操作
- **屏幕阅读器**: 提供适当的aria属性
- **颜色对比**: 确保足够的颜色对比度
