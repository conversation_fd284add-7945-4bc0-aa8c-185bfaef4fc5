---
description: 
globs: 
alwaysApply: false
---
# 客舱设施与服务五力评价系统 - 项目概览

## 项目简介
这是一个基于 Vue3 + TypeScript 开发的航空职业教育评价平台，用于对学生的客舱设施与服务能力进行多维度评价。

## 核心功能
- **多身份评价**: 支持学生、教师、企业三种身份评价，权重分别为 10%、50%、40%
- **五力评价模型**: 管理力、执行力、观察力、沟通力、应变力五个维度
- **实时评分**: 支持滑块式评分，实时保存评价数据到 localStorage
- **数据可视化**: 提供雷达图、柱状图、折线图、饼图等多种图表展示
- **Excel导出**: 支持评价数据导出为Excel文件

## 技术栈
- **前端框架**: Vue 3 (Composition API + `<script setup>`)
- **开发语言**: TypeScript (完整类型定义)
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **样式框架**: Tailwind CSS + 自定义样式
- **图表库**: Chart.js + vue-chartjs
- **Excel导出**: XLSX
- **构建工具**: Vite

## 项目来源
本项目由豆包生成的HTML文件 [demo.html](mdc:demo.html) 转换而来，保持了原有的所有功能和UI设计，但采用了现代化的Vue3架构。

## 开发规范
- 使用 Composition API 和 `<script setup>` 语法
- 严格的 TypeScript 类型定义
- 响应式设计，支持移动端和桌面端
- 所有样式使用 Tailwind CSS 类名或自定义CSS类

