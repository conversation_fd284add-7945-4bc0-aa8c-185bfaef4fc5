import request from '@/utils/request'

/**
 * 查询指标列表
 * @param {Object} [params] - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 页面大小
 * @returns {Promise<import('@/types').ApiResponse<import('@/types').ApiIndicator>>} 指标列表
 */
export function getIndicatorList(params) {
  return request({
    url: '/hk/indicator/list',
    method: 'get',
    params,
  })
}

/**
 * 查询学生列表
 * @param {Object} [params] - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 页面大小
 * @returns {Promise<import('@/types').ApiResponse<import('@/types').ApiStudent>>} 学生列表
 */
export function getStudentList(params) {
  return request({
    url: '/hk/students/list',
    method: 'get',
    params,
  })
}
