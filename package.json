{"name": "aviation-evaluation-system-frontend", "author": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .js,.vue", "lint:fix": "eslint . --ext .js,.vue --fix"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@fortawesome/fontawesome-free": "^6.7.2", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "chart.js": "^4.5.0", "echarts": "^5.6.0", "pinia": "^3.0.3", "tw-animate-css": "^1.3.5", "vue": "^3.5.17", "vue-chartjs": "^5.3.2", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-vue": "^6.0.0", "@vue/runtime-core": "^3.5.17", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "prettier": "^3.6.2", "sass": "^1.89.2", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "vite": "^7.0.3", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^10.2.0"}}