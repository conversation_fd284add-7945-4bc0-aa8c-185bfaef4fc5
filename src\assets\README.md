# 静态资源模块 (Assets)

该目录包含项目的所有静态资源文件，包括样式文件、图片、字体等资源。

## 📁 文件结构

```
src/assets/
└── styles/
    ├── main.scss        # 主样式文件
    └── tailwind.css     # Tailwind CSS 配置文件
```

## 🎨 样式系统

### 1. 样式架构
项目采用 **Tailwind CSS + SCSS** 的混合样式架构：
- **Tailwind CSS**: 提供原子化CSS类，用于快速构建UI
- **SCSS**: 处理复杂样式逻辑和全局样式定义
- **组件样式**: 在Vue组件中使用scoped样式

### 2. 样式优先级
1. **Tailwind原子类** - 优先使用，提供一致性和可维护性
2. **全局SCSS样式** - 处理Tailwind无法覆盖的场景
3. **组件scoped样式** - 组件特定的样式定制

## 📄 文件详解

### `styles/tailwind.css` - Tailwind CSS 配置

**功能**: Tailwind CSS的入口文件和自定义配置

**内容**:
```css
@import "tailwindcss";
@config "../../../tailwind.config.js";
@import "tw-animate-css";
```

**说明**:
- `@import "tailwindcss"` - 导入Tailwind CSS核心样式
- `@config` - 指定Tailwind配置文件路径
- `@import "tw-animate-css"` - 导入动画库

**配置文件**: [`tailwind.config.js`](../../tailwind.config.js)
- 定义了项目的设计系统（颜色、字体、间距等）
- 配置了自定义主题色彩
- 启用了必要的插件和功能

### `styles/main.scss` - 主样式文件

**功能**: 全局样式定义和字体配置

**内容**:
```scss
* {
  font-family: "Inter", "Helvetica Neue", "Helvetica", "Roboto", 
               "BlinkMacSystemFont", "MiSans", "HarmonyOS Sans SC", 
               "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", 
               "微软雅黑", Arial, sans-serif;
}
```

**特点**:
- **多语言字体支持**: 优先使用现代字体，回退到系统字体
- **中文字体优化**: 包含HarmonyOS Sans SC、PingFang SC等现代中文字体
- **跨平台兼容**: 支持Windows、macOS、Linux等不同操作系统

**字体优先级**:
1. `Inter` - 现代无衬线字体，优秀的可读性
2. `Helvetica Neue` / `Helvetica` - 经典系统字体
3. `Roboto` - Android系统字体
4. `HarmonyOS Sans SC` - 华为鸿蒙系统中文字体
5. `PingFang SC` - macOS中文字体
6. `Microsoft YaHei` - Windows中文字体

## 🎯 设计系统

### 1. 颜色系统
项目使用Tailwind CSS的颜色系统，主要颜色定义在 `tailwind.config.js` 中：

```javascript
// 主要颜色（示例）
colors: {
  primary: '#1890ff',      // 主色调 - 蓝色
  secondary: '#52c41a',    // 辅助色 - 绿色
  danger: '#ff4d4f',       // 危险色 - 红色
  warning: '#faad14',      // 警告色 - 橙色
  // ... 更多颜色定义
}
```

### 2. 间距系统
使用Tailwind的标准间距系统：
- `p-4` = padding: 1rem
- `m-2` = margin: 0.5rem
- `space-x-4` = 子元素间距: 1rem

### 3. 响应式设计
使用Tailwind的响应式前缀：
- `sm:` - 640px及以上
- `md:` - 768px及以上
- `lg:` - 1024px及以上
- `xl:` - 1280px及以上

## 🚀 使用指南

### 1. 在组件中使用样式

**推荐方式 - Tailwind原子类**:
```vue
<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold text-gray-800 mb-4">标题</h2>
    <p class="text-gray-600 leading-relaxed">内容文本</p>
  </div>
</template>
```

**自定义样式 - Scoped SCSS**:
```vue
<template>
  <div class="custom-card">
    <h2 class="card-title">标题</h2>
  </div>
</template>

<style lang="scss" scoped>
.custom-card {
  @apply bg-white p-6 rounded-lg shadow-md;
  
  .card-title {
    @apply text-2xl font-bold text-gray-800 mb-4;
    
    // 自定义样式
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
}
</style>
```

### 2. 全局样式添加
如需添加全局样式，在 `main.scss` 中添加：

```scss
// 添加到 src/assets/styles/main.scss
.global-button {
  @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600;
  transition: background-color 0.2s ease;
}
```

### 3. 主题定制
修改 `tailwind.config.js` 来定制主题：

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        'brand-blue': '#1890ff',
        'brand-green': '#52c41a',
      },
      fontFamily: {
        'custom': ['Inter', 'sans-serif'],
      }
    }
  }
}
```

## 📱 响应式设计

### 1. 断点系统
```css
/* 移动端优先 */
.container {
  @apply px-4;          /* 默认移动端 */
  @apply sm:px-6;       /* 小屏幕 */
  @apply md:px-8;       /* 中等屏幕 */
  @apply lg:px-12;      /* 大屏幕 */
}
```

### 2. 常用响应式模式
```vue
<template>
  <!-- 响应式网格 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <!-- 网格项目 -->
  </div>
  
  <!-- 响应式文字 -->
  <h1 class="text-xl md:text-2xl lg:text-3xl">响应式标题</h1>
  
  <!-- 响应式显示/隐藏 -->
  <div class="hidden md:block">桌面端显示</div>
  <div class="block md:hidden">移动端显示</div>
</template>
```

## 🔧 开发工具

### 1. VS Code 扩展推荐
- **Tailwind CSS IntelliSense** - 提供类名自动补全
- **SCSS IntelliSense** - SCSS语法支持
- **PostCSS Language Support** - PostCSS语法高亮

### 2. 样式调试
```javascript
// 在浏览器控制台中查看Tailwind配置
// 开发模式下可用
console.log(tailwind.config)
```

## 🔗 相关文件

- [`tailwind.config.js`](../../tailwind.config.js) - Tailwind CSS配置
- [`src/main.js`](../main.js) - 样式文件导入
- [`vite.config.js`](../../vite.config.js) - 构建工具配置

## 📝 开发规范

### 1. 样式编写规范
- **优先使用Tailwind原子类**，减少自定义CSS
- **使用@apply指令**将Tailwind类组合成可复用的样式
- **避免内联样式**，保持模板的清洁
- **使用scoped样式**避免样式污染

### 2. 文件组织规范
- **全局样式** → `src/assets/styles/main.scss`
- **组件样式** → 组件文件内的`<style scoped>`
- **工具样式** → 通过Tailwind配置扩展

### 3. 命名规范
- **CSS类名**: 使用kebab-case，如 `.custom-button`
- **SCSS变量**: 使用kebab-case，如 `$primary-color`
- **Tailwind自定义**: 使用kebab-case，如 `brand-blue`

### 4. 性能优化
- **使用Tailwind的purge功能**移除未使用的样式
- **避免深层嵌套**的SCSS选择器
- **合理使用@apply**，避免生成重复的CSS
