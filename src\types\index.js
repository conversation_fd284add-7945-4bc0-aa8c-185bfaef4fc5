/**
 * 学生信息类型
 * @typedef {Object} Student
 * @property {string} id - 学生ID
 * @property {string} name - 学生姓名
 */

/**
 * 评价身份类型
 * @typedef {'student' | 'teacher' | 'enterprise'} Role
 */

/**
 * 评价指标类型
 * @typedef {Object} Indicator
 * @property {string} id - 指标ID
 * @property {string} name - 指标名称
 * @property {string} criteria - 评价标准
 */

/**
 * 评价维度类型
 * @typedef {Object} Dimension
 * @property {string} id - 维度ID
 * @property {string} name - 维度名称
 * @property {Indicator[]} indicators - 指标列表
 */

/**
 * 单个维度评分数据
 * @typedef {Object} DimensionScore
 * @property {boolean} completed - 是否完成评分
 * @property {number} [indicatorId] - 指标评分（动态属性）
 */

/**
 * 角色评分数据
 * @typedef {Object} RoleScores
 * @property {DimensionScore} management - 管理维度评分
 * @property {DimensionScore} execution - 执行维度评分
 * @property {DimensionScore} observation - 观察维度评分
 * @property {DimensionScore} communication - 沟通维度评分
 * @property {DimensionScore} adaptability - 适应性维度评分
 */

/**
 * 学生完整评价数据
 * @typedef {Object} StudentEvaluationData
 * @property {string} name - 学生姓名
 * @property {Object} scores - 各角色评分
 * @property {RoleScores} scores.student - 学生评分
 * @property {RoleScores} scores.teacher - 教师评分
 * @property {RoleScores} scores.enterprise - 企业评分
 * @property {Object} status - 各角色评价状态
 * @property {string} status.student - 学生评价状态
 * @property {string} status.teacher - 教师评价状态
 * @property {string} status.enterprise - 企业评价状态
 */

/**
 * 全局状态类型
 * @typedef {Object} AppState
 * @property {number} currentPage - 当前页面
 * @property {Role|null} selectedRole - 选中的角色
 * @property {string|null} selectedStudentId - 选中的学生ID
 * @property {boolean} isViewingScores - 是否正在查看分数
 * @property {number} currentDimension - 当前维度
 * @property {Record<string, StudentEvaluationData>} evaluationData - 评价数据
 */

/**
 * 身份权重配置
 * @typedef {Object} RoleWeight
 * @property {Role} role - 角色类型
 * @property {string} name - 角色名称
 * @property {number} weight - 权重
 * @property {string} icon - 图标
 */

/**
 * 图表数据类型
 * @typedef {Object} ChartData
 * @property {string[]} labels - 标签数组
 * @property {any[]} datasets - 数据集数组
 */

/**
 * API指标数据 (来自 /hk/indicator/list)
 * @typedef {Object} ApiIndicator
 * @property {number} id - 指标ID
 * @property {string} service - 服务
 * @property {string} dimensionality - 维度
 * @property {string} evaluatingIndicator - 评价指标
 * @property {string} ratingCriteria - 评分标准
 */

/**
 * 通用API响应格式
 * @template T
 * @typedef {Object} ApiResponse
 * @property {number} total - 总数
 * @property {T[]} rows - 数据行
 * @property {number} code - 响应码
 * @property {string} msg - 响应消息
 */

/**
 * 登录数据
 * @typedef {Object} LoginData
 * @property {string} [username] - 用户名
 * @property {string} [password] - 密码
 * @property {string} [code] - 验证码
 * @property {string} [uuid] - UUID
 */

/**
 * 登录响应
 * @typedef {Object} LoginResponse
 * @property {number} code - 响应码
 * @property {string} msg - 响应消息
 * @property {string} token - 访问令牌
 */

/**
 * 用户档案API响应
 * @typedef {Object} UserProfile
 * @property {string} msg - 响应消息
 * @property {string} postGroup - 岗位组
 * @property {number} code - 响应码
 * @property {UserData} data - 用户数据
 * @property {string} roleGroup - 角色组
 */

/**
 * 用户数据
 * @typedef {Object} UserData
 * @property {string} createBy - 创建者
 * @property {string} createTime - 创建时间
 * @property {string|null} updateBy - 更新者
 * @property {string|null} updateTime - 更新时间
 * @property {string} remark - 备注
 * @property {Record<string, any>} params - 参数
 * @property {number} userId - 用户ID
 * @property {number} deptId - 部门ID
 * @property {string} userName - 用户名
 * @property {string} nickName - 昵称
 * @property {string} email - 邮箱
 * @property {string} phonenumber - 手机号
 * @property {string} sex - 性别
 * @property {string|null} avatar - 头像
 * @property {string} status - 状态
 * @property {string} delFlag - 删除标志
 * @property {string} loginIp - 登录IP
 * @property {string} loginDate - 登录日期
 * @property {string} pwdUpdateDate - 密码更新日期
 * @property {Dept} dept - 部门信息
 * @property {RoleInfo[]} roles - 角色列表
 * @property {number[]|null} roleIds - 角色ID列表
 * @property {number[]|null} postIds - 岗位ID列表
 * @property {number|null} roleId - 角色ID
 * @property {boolean} admin - 是否管理员
 */

/**
 * 部门信息
 * @typedef {Object} Dept
 * @property {string|null} createBy - 创建者
 * @property {string|null} createTime - 创建时间
 * @property {string|null} updateBy - 更新者
 * @property {string|null} updateTime - 更新时间
 * @property {string|null} remark - 备注
 * @property {Record<string, any>} params - 参数
 * @property {number} deptId - 部门ID
 * @property {number} parentId - 父部门ID
 * @property {string} ancestors - 祖级列表
 * @property {string} deptName - 部门名称
 * @property {number} orderNum - 显示顺序
 * @property {string} leader - 负责人
 * @property {string|null} phone - 联系电话
 * @property {string|null} email - 邮箱
 * @property {string} status - 部门状态
 * @property {string|null} delFlag - 删除标志
 * @property {string|null} parentName - 父部门名称
 * @property {any[]} children - 子部门
 */

/**
 * 角色信息
 * @typedef {Object} RoleInfo
 * @property {string|null} createBy - 创建者
 * @property {string|null} createTime - 创建时间
 * @property {string|null} updateBy - 更新者
 * @property {string|null} updateTime - 更新时间
 * @property {string|null} remark - 备注
 * @property {Record<string, any>} params - 参数
 * @property {number} roleId - 角色ID
 * @property {string} roleName - 角色名称
 * @property {string} roleKey - 角色权限字符串
 * @property {number} roleSort - 显示顺序
 * @property {string} dataScope - 数据范围
 * @property {boolean} menuCheckStrictly - 菜单树选择项是否关联显示
 * @property {boolean} deptCheckStrictly - 部门树选择项是否关联显示
 * @property {string} status - 角色状态
 * @property {string|null} delFlag - 删除标志
 * @property {boolean} flag - 用户是否存在此角色标识
 * @property {number[]|null} menuIds - 菜单组
 * @property {number[]|null} deptIds - 部门组
 * @property {string[]|null} permissions - 权限组
 * @property {boolean} admin - 是否管理员
 */
