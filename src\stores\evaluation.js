import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { listUser } from '@/api/user'
import { listMarks, addMarks, updateMarks } from '@/api/marks'
import { getServiceList } from '@/api/service'
import { getIndicatorList, getDimensionalityList } from '@/api/indicator'
import { useAuthStore } from '@/stores/auth'
/**
 * @typedef {import('@/types').Role} Role
 * @typedef {import('@/types').Student} Student
 * @typedef {import('@/api/marks').Marks} Marks
 */

export const useEvaluationStore = defineStore('evaluation', () => {
  // 获取auth store实例
  const authStore = useAuthStore()

  /**
   * 根据选择的角色获取中文的identity值
   * @returns {string} 中文身份标识
   */
  const getChineseIdentity = () => {
    if (!selectedRole.value) return '未知'

    // 将英文角色转换为中文身份标识
    switch (selectedRole.value.value) {
      case 'student': return '学生'
      case 'teacher': return '教师'
      case 'enterprise': return '企业'
      default: return '未知'
    }
  }

  // 状态
  /** @type {import('vue').Ref<Student[]>} */
  const students = ref([])
  /** @type {import('vue').Ref<number>} */
  const total = ref(0)
  /** @type {import('vue').Ref<object>} */
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    deptId: 201,
  })
  /** @type {import('vue').Ref<Role | null>} */
  const selectedRole = ref(null)
  /** @type {import('vue').Ref<string | null>} */
  const selectedStudentId = ref(null)
  /** @type {import('vue').Ref<boolean>} */
  const isViewingScores = ref(false)
  /** @type {import('vue').Ref<number>} */
  const currentDimension = ref(0)
  // 新的状态，用于存储从API获取的成绩
  /** @type {import('vue').Ref<Marks[]>} */
  const marks = ref([])

  // 新增：服务相关状态
  /** @type {import('vue').Ref<Array>} */
  const services = ref([])
  /** @type {import('vue').Ref<string | null>} */
  const selectedService = ref(null)
  /** @type {import('vue').Ref<number>} */
  const currentStep = ref(1) // 1: 选择服务, 2: 选择角色, 3: 选择学生

  // 新增：指标和维度相关状态
  /** @type {import('vue').Ref<Array>} */
  const indicators = ref([])
  /** @type {import('vue').Ref<Array>} */
  const dimensionalities = ref([])
  /** @type {import('vue').Ref<number>} */
  const currentIndicatorIndex = ref(0)

  // 新增：学生评价状态缓存
  /** @type {import('vue').Ref<Record<string, {completed: number, total: number, status: string}>>} */
  const studentEvaluationStatus = ref({})

  // 计算属性
  const selectedStudent = computed(() => {
    if (!selectedStudentId.value) return null
    // 学生ID现在是学号（userName），是字符串。
    return students.value.find((s) => s.id === selectedStudentId.value) || null
  })

  const evaluationProgress = computed(() => {
    if (!selectedRole.value || !selectedStudent.value || !selectedService.value)
      return { completed: 0, total: indicators.value.length }

    // 使用选择的角色对应的中文身份标识进行过滤
    const chineseIdentity = getChineseIdentity()
    const studentMarks = marks.value.filter((m) => m.identity === chineseIdentity)

    // 计算已完成的指标数量
    const completedIndicators = indicators.value.filter((indicator) => {
      return studentMarks.some(
        (m) =>
          m.evaluatingIndicator === indicator.evaluatingIndicator &&
          m.dimensionality === indicator.dimensionality &&
          m.service === indicator.service
      )
    }).length

    return {
      completed: completedIndicators,
      total: indicators.value.length,
    }
  })

  // Actions
  const fetchServices = async () => {
    try {
      const response = await getServiceList()
      services.value = response.data || []
    } catch (error) {
      console.error('Failed to fetch services:', error)
      services.value = []
    }
  }

  const fetchDimensionalities = async () => {
    try {
      const response = await getDimensionalityList()
      dimensionalities.value = response.data || []
    } catch (error) {
      console.error('Failed to fetch dimensionalities:', error)
      dimensionalities.value = []
    }
  }

  const fetchIndicators = async () => {
    if (!selectedService.value) {
      indicators.value = []
      return
    }
    try {
      // 获取第一页数据
      const firstResponse = await getIndicatorList(selectedService.value.dictValue, { pageNum: 1, pageSize: 10 })
      const total = firstResponse.total || 0
      let allIndicators = firstResponse.rows || []

      // 如果总数超过10，获取第二页数据
      if (total > 10) {
        const secondResponse = await getIndicatorList(selectedService.value.dictValue, { pageNum: 2, pageSize: 10 })
        allIndicators = [...allIndicators, ...(secondResponse.rows || [])]
      }

      // 如果还有更多页，继续获取（以防万一有超过20题的服务）
      if (total > 20) {
        const remainingPages = Math.ceil((total - 20) / 10)
        for (let page = 3; page <= 2 + remainingPages; page++) {
          const response = await getIndicatorList(selectedService.value.dictValue, { pageNum: page, pageSize: 10 })
          allIndicators = [...allIndicators, ...(response.rows || [])]
        }
      }

      indicators.value = allIndicators
      currentIndicatorIndex.value = 0
      console.log(`Loaded ${indicators.value.length} indicators for service: ${selectedService.value.dictValue}`)
    } catch (error) {
      console.error('Failed to fetch indicators:', error)
      indicators.value = []
    }
  }

  const fetchStudents = async () => {
    try {
      const response = await listUser(queryParams.value)
      const studentList = response.rows.map((s) => ({
        id: s.userName, // 学号
        name: s.nickName, // 姓名
      }))

      // 如果当前用户是学生，将自己的信息置顶
      if (authStore.userInfo && authStore.userInfo.deptId === 201) {
        const currentUserNum = authStore.userInfo.userName
        const currentUserName = authStore.userInfo.nickName

        // 从列表中移除当前用户（如果存在）
        const otherStudents = studentList.filter(s => s.id !== currentUserNum)

        // 将当前用户信息添加到列表顶部
        students.value = [
          {
            id: currentUserNum,
            name: currentUserName,
            isSelf: true // 标记为自己
          },
          ...otherStudents
        ]
      } else {
        students.value = studentList
      }

      total.value = response.total
    } catch (error) {
      console.error('Failed to fetch students:', error)
    }
  }

  // 获取一个学生的所有成绩
  const fetchMarks = async (filterByCurrentUser = true) => {
    if (!selectedStudent.value || !selectedService.value) {
      marks.value = []
      return
    }
    try {
      // 获取所有分页的成绩数据
      let allMarks = []
      let pageNum = 1
      const pageSize = 50 // 增加pageSize到50

      while (true) {
        const queryParams = {
          pageNum,
          pageSize,
          num: selectedStudent.value.id, // 被评价学生的学号
          service: selectedService.value.dictValue
        }

        // 如果需要筛选当前用户的评价记录（评价模式）
        if (filterByCurrentUser) {
          queryParams.srcNun = authStore.userInfo?.userName || '' // 评价者学号
          queryParams.srcNane = authStore.userInfo?.nickName || '' // 评价者姓名
        }

        const response = await listMarks(queryParams)

        const pageMarks = response.rows || []
        const total = response.total || 0

        allMarks = [...allMarks, ...pageMarks]

        console.log(`fetchMarks: 第${pageNum}页，获取到${pageMarks.length}条记录，总共${total}条，已获取${allMarks.length}条`)

        // 如果已获取的记录数达到总数，或者当前页记录数为0，则停止
        if (allMarks.length >= total || pageMarks.length === 0) {
          break
        }

        // 如果当前页的记录数少于pageSize，说明已经是最后一页
        if (pageMarks.length < pageSize) {
          break
        }

        pageNum++
      }

      marks.value = allMarks
      console.log(`fetchMarks完成: 总共获取到${allMarks.length}条成绩记录`)
    } catch (error) {
      console.error('Failed to fetch marks:', error)
      marks.value = []
    }
  }

  // 获取单个学生的评价状态（现在主要用于单独查询，批量查询使用fetchAllStudentsEvaluationStatus）
  const fetchStudentEvaluationStatus = async (studentNum, identity) => {
    if (!selectedService.value) return { completed: 0, total: 0, status: '未评价' }

    // 如果已经有缓存的状态，直接返回
    if (studentEvaluationStatus.value[studentNum]) {
      return studentEvaluationStatus.value[studentNum]
    }

    try {
      // 获取题目总数（如果还没有获取过）
      if (indicators.value.length === 0) {
        await fetchIndicators()
      }
      const totalQuestions = indicators.value.length

      // 获取该学生在该身份下的所有评价记录
      let allMarks = []
      let pageNum = 1
      const pageSize = 50

      while (true) {
        const response = await listMarks({
          pageNum,
          pageSize,
          service: selectedService.value.dictValue,
          identity,
          // 添加评价者信息筛选，只获取当前用户的评价记录
          srcNun: authStore.userInfo?.userName || '', // 评价者学号
          srcNane: authStore.userInfo?.nickName || '' // 评价者姓名
        })

        const marks = response.rows || []
        const total = response.total || 0

        // 过滤出该学生的记录
        const studentMarks = marks.filter(mark => mark.num.toString() === studentNum.toString())
        allMarks = [...allMarks, ...studentMarks]

        // 如果已获取的记录数达到总数，或者当前页记录数为0，则停止
        if (marks.length >= total || marks.length === 0) {
          break
        }

        // 如果当前页的记录数少于pageSize，说明已经是最后一页
        if (marks.length < pageSize) {
          break
        }

        pageNum++
      }

      const completedQuestions = allMarks.length
      let status = '未评价'
      if (completedQuestions === 0) {
        status = '未评价'
      } else if (completedQuestions < totalQuestions) {
        status = '进行中'
      } else {
        status = '已完成'
      }

      const result = {
        completed: completedQuestions,
        total: totalQuestions,
        status
      }

      // 缓存结果
      studentEvaluationStatus.value[studentNum] = result

      return result
    } catch (error) {
      console.error('Failed to fetch student evaluation status:', error)
      return { completed: 0, total: 0, status: '未评价' }
    }
  }

  // 批量获取所有学生的评价状态
  const fetchAllStudentsEvaluationStatus = async () => {
    if (!selectedRole.value?.value || !selectedService.value || students.value.length === 0) {
      return
    }

    const identity = getChineseIdentity()

    // 清空之前的状态
    studentEvaluationStatus.value = {}

    try {
      // 获取题目总数（如果还没有获取过）
      if (indicators.value.length === 0) {
        await fetchIndicators()
      }
      const totalQuestions = indicators.value.length

      // 一次性获取该身份下的所有评价记录
      let allMarks = []
      let pageNum = 1
      const pageSize = 50

      console.log(`开始获取${identity}身份下的所有评价记录...`)

      while (true) {
        const response = await listMarks({
          pageNum,
          pageSize,
          service: selectedService.value.dictValue,
          identity,
          // 添加评价者信息筛选，只获取当前用户的评价记录
          srcNun: authStore.userInfo?.userName || '', // 评价者学号
          srcNane: authStore.userInfo?.nickName || '' // 评价者姓名
        })

        const marks = response.rows || []
        const total = response.total || 0

        allMarks = [...allMarks, ...marks]

        console.log(`第${pageNum}页，获取到${marks.length}条记录，总共${total}条，已获取${allMarks.length}条`)

        // 如果已获取的记录数达到总数，或者当前页记录数为0，则停止
        if (allMarks.length >= total || marks.length === 0) {
          break
        }

        // 如果当前页的记录数少于pageSize，说明已经是最后一页
        if (marks.length < pageSize) {
          break
        }

        pageNum++
      }

      console.log(`获取完成，总共${allMarks.length}条${identity}评价记录`)

      // 按学生分组统计评价状态
      const studentMarksMap = {}
      allMarks.forEach(mark => {
        const studentNum = mark.num.toString()
        if (!studentMarksMap[studentNum]) {
          studentMarksMap[studentNum] = []
        }
        studentMarksMap[studentNum].push(mark)
      })

      // 为每个学生计算评价状态
      students.value.forEach(student => {
        const studentMarks = studentMarksMap[student.id.toString()] || []
        const completedQuestions = studentMarks.length

        let status = '未评价'
        if (completedQuestions === 0) {
          status = '未评价'
        } else if (completedQuestions < totalQuestions) {
          status = '进行中'
        } else {
          status = '已完成'
        }

        studentEvaluationStatus.value[student.id] = {
          completed: completedQuestions,
          total: totalQuestions,
          status
        }
      })

      console.log(`学生评价状态统计完成，共处理${students.value.length}个学生`)

    } catch (error) {
      console.error('Failed to fetch all students evaluation status:', error)
    }
  }

  // 提交单个指标的成绩
  const submitIndicatorMark = async (indicator, ratingCriteriaMark) => {
    if (!selectedRole.value || !selectedStudent.value || !selectedService.value) return

    try {
      // 使用选择的角色对应的中文身份标识
      const chineseIdentity = getChineseIdentity()

      const markData = {
        id: null,
        num: selectedStudent.value.id, // 被评价学生的学号
        name: selectedStudent.value.name, // 被评价学生的姓名
        dimensionality: indicator.dimensionality,
        service: selectedService.value.dictValue,
        evaluatingIndicator: indicator.evaluatingIndicator,
        identity: chineseIdentity, // 使用选择的角色对应的中文身份标识
        ratingCriteriaMark: ratingCriteriaMark,
        // 添加评价者信息（当前用户信息）
        srcNane: authStore.userInfo?.nickName || '', // 评价者姓名（注意后端字段名拼写错误）
        srcNun: authStore.userInfo?.userName || ''   // 评价者学号（注意后端字段名拼写错误）
      }

      await addMarks(markData)
      // 刷新成绩列表（评价模式下只获取当前用户的评价记录）
      await fetchMarks(true)
      return true
    } catch (error) {
      console.error('Failed to submit indicator mark:', error)
      throw error
    }
  }

  // 检查指标是否已完成
  const isIndicatorCompleted = (indicator) => {
    if (!selectedRole.value || !selectedStudent.value) return false

    // 使用选择的角色对应的中文身份标识进行比较
    const chineseIdentity = getChineseIdentity()
    return marks.value.some(
      (m) =>
        m.evaluatingIndicator === indicator.evaluatingIndicator &&
        m.dimensionality === indicator.dimensionality &&
        m.service === indicator.service &&
        m.identity === chineseIdentity
    )
  }

  // 获取指标的已提交成绩
  const getIndicatorMark = (indicator) => {
    if (!selectedRole.value || !selectedStudent.value) return null

    // 使用选择的角色对应的中文身份标识进行查找
    const chineseIdentity = getChineseIdentity()
    return marks.value.find(
      (m) =>
        m.evaluatingIndicator === indicator.evaluatingIndicator &&
        m.dimensionality === indicator.dimensionality &&
        m.service === indicator.service &&
        m.identity === chineseIdentity
    )
  }

  // 获取学生的评价状态
  const getStudentEvaluationStatus = (studentId) => {
    return studentEvaluationStatus.value[studentId] || { completed: 0, total: 0, status: '未评价' }
  }

  // 设置当前指标索引
  const setCurrentIndicatorIndex = (index) => {
    currentIndicatorIndex.value = index
  }

  // 将分数转换为等级
  const scoreToGrade = (score) => {
    if (score >= 90) return 'A'
    if (score >= 70) return 'B'
    if (score >= 50) return 'C'
    if (score >= 30) return 'D'
    if (score > 0) return 'E'
    return '-' // 无评分
  }

  // 计算维度平均分
  const calculateDimensionAverage = (role, dimensionality) => {
    if (!selectedStudent.value || !selectedService.value) return 0

    // 将英文角色转换为中文身份标识
    let chineseRole
    switch (role) {
      case 'student': chineseRole = '学生'; break
      case 'teacher': chineseRole = '教师'; break
      case 'enterprise': chineseRole = '企业'; break
      default: chineseRole = role // 如果已经是中文，直接使用
    }

    // 获取该角色、该维度的所有成绩
    const dimensionMarks = marks.value.filter(
      (m) =>
        m.identity === chineseRole &&
        m.dimensionality === dimensionality &&
        m.service === selectedService.value.dictValue
    )

    if (dimensionMarks.length === 0) return 0

    // 将评分标准转换为数值分数
    const scores = dimensionMarks.map(mark => {
      const rating = mark.ratingCriteriaMark
      switch (rating) {
        case 'A': return 100
        case 'B': return 80
        case 'C': return 60
        case 'D': return 40
        case 'E': return 20
        default: return 0
      }
    })

    // 计算平均分
    return scores.reduce((sum, score) => sum + score, 0) / scores.length
  }

  // 计算维度综合评分（按权重计算后转换为等级）
  const calculateDimensionGrade = (dimensionality) => {
    if (!selectedStudent.value || !selectedService.value) return '-'

    const studentAvg = calculateDimensionAverage('student', dimensionality)
    const teacherAvg = calculateDimensionAverage('teacher', dimensionality)
    const enterpriseAvg = calculateDimensionAverage('enterprise', dimensionality)

    // 如果没有任何评分，返回'-'
    if (studentAvg === 0 && teacherAvg === 0 && enterpriseAvg === 0) {
      return '-'
    }

    // 按权重计算综合分数：学生10%，教师50%，企业40%
    const weightedScore = (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4)

    // 转换为等级
    return scoreToGrade(weightedScore)
  }

  // 计算总分（返回等级）
  const calculateTotalGrade = () => {
    if (!selectedStudent.value || !selectedService.value) return '-'

    // 获取所有维度
    const dimensionalities = [...new Set(indicators.value.map(i => i.dimensionality))]

    if (dimensionalities.length === 0) return '-'

    let totalScore = 0
    let validDimensions = 0

    dimensionalities.forEach(dimensionality => {
      const studentAvg = calculateDimensionAverage('student', dimensionality)
      const teacherAvg = calculateDimensionAverage('teacher', dimensionality)
      const enterpriseAvg = calculateDimensionAverage('enterprise', dimensionality)

      // 如果至少有一个角色有评分，则计算该维度分数
      if (studentAvg > 0 || teacherAvg > 0 || enterpriseAvg > 0) {
        const dimensionScore = (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4)
        totalScore += dimensionScore
        validDimensions++
      }
    })

    if (validDimensions === 0) return '-'

    // 计算平均分
    const averageScore = totalScore / validDimensions

    // 转换为等级
    return scoreToGrade(averageScore)
  }

  // 保留原来的数值计算函数（用于兼容）
  const calculateTotalScore = () => {
    if (!selectedStudent.value || !selectedService.value) return 0

    // 获取所有维度
    const dimensionalities = [...new Set(indicators.value.map(i => i.dimensionality))]

    let totalScore = 0
    let validDimensions = 0

    dimensionalities.forEach(dimensionality => {
      const studentAvg = calculateDimensionAverage('student', dimensionality)
      const teacherAvg = calculateDimensionAverage('teacher', dimensionality)
      const enterpriseAvg = calculateDimensionAverage('enterprise', dimensionality)

      // 如果至少有一个角色有评分，则计算该维度分数
      if (studentAvg > 0 || teacherAvg > 0 || enterpriseAvg > 0) {
        const dimensionScore = (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4)
        totalScore += dimensionScore
        validDimensions++
      }
    })

    return validDimensions > 0 ? totalScore / validDimensions : 0
  }

  const selectService = async (service) => {
    selectedService.value = service
    // 将选择的服务保存到localStorage
    localStorage.setItem('selectedService', JSON.stringify(service))
    // 获取该服务的指标列表
    await fetchIndicators()
    currentStep.value = 2 // 进入角色选择步骤
  }

  const selectRole = (roleValue) => {
    // 创建角色对象
    selectedRole.value = { value: roleValue }
    isViewingScores.value = false
    currentStep.value = 3 // 进入学生选择步骤
  }

  const setCurrentStep = (step) => {
    currentStep.value = step
  }

  // 重置角色和学生选择，但保留服务选择
  const resetRoleAndStudentSelection = () => {
    selectedRole.value = null
    selectedStudentId.value = null
    isViewingScores.value = false
    currentStep.value = 2 // 回到角色选择步骤
    marks.value = []
    // 清空学生评价状态缓存
    studentEvaluationStatus.value = {}
  }

  const selectStudent = async (studentId, studentInfo = null, doFetchMarks = true) => {
    if (studentInfo && !students.value.some(s => s.id === studentId)) {
      // 如果提供了学生信息，并且该学生不在列表中，则将他/她添加到列表
      // 这主要用于学生给自己评价的场景
      students.value.unshift(studentInfo)
    }
    selectedStudentId.value = studentId
    // 选择学生后，根据需要获取他的成绩
    if (doFetchMarks) {
      // 如果是查看成绩模式，获取所有人的评价记录；如果是评价模式，只获取当前用户的评价记录
      const filterByCurrentUser = !isViewingScores.value
      await fetchMarks(filterByCurrentUser)
    }
  }

  const setViewingScores = (viewing) => {
    isViewingScores.value = viewing
  }

  const setCurrentDimension = (index) => {
    currentDimension.value = index
  }

  const resetSelection = () => {
    selectedService.value = null
    selectedRole.value = null
    selectedStudentId.value = null
    isViewingScores.value = false
    currentDimension.value = 0
    currentStep.value = 1
    marks.value = []
    services.value = []
    indicators.value = []
    dimensionalities.value = []
    currentIndicatorIndex.value = 0
    queryParams.value = {
      pageNum: 1,
      pageSize: 10,
      deptId: 201,
    }
    // 清除localStorage中的服务选择
    localStorage.removeItem('selectedService')
  }

  // 初始化时从localStorage恢复服务选择
  const initializeFromStorage = async () => {
    // 只有在有token的情况下才恢复状态
    const token = localStorage.getItem('token')
    if (!token) {
      currentStep.value = 1
      return
    }

    const savedService = localStorage.getItem('selectedService')
    if (savedService) {
      try {
        selectedService.value = JSON.parse(savedService)
        // 恢复服务后，需要重新获取指标列表
        await fetchIndicators()
        currentStep.value = 2 // 如果有保存的服务，直接进入角色选择
      } catch (error) {
        console.error('Failed to parse saved service:', error)
        localStorage.removeItem('selectedService')
        currentStep.value = 1
      }
    } else {
      currentStep.value = 1
    }
  }

  const setQueryParams = (params) => {
    queryParams.value = { ...queryParams.value, ...params }
    fetchStudents()
  }

  return {
    // State
    students,
    total,
    queryParams,
    selectedRole,
    selectedStudentId,
    isViewingScores,
    currentDimension,
    marks,
    services,
    selectedService,
    currentStep,
    indicators,
    dimensionalities,
    currentIndicatorIndex,
    studentEvaluationStatus,

    // Computed
    selectedStudent,
    evaluationProgress,

    // Actions
    fetchServices,
    fetchDimensionalities,
    fetchIndicators,
    fetchStudents,
    fetchMarks,
    fetchStudentEvaluationStatus,
    fetchAllStudentsEvaluationStatus,
    getStudentEvaluationStatus,
    submitIndicatorMark,
    isIndicatorCompleted,
    getIndicatorMark,
    setCurrentIndicatorIndex,
    calculateDimensionAverage,
    calculateDimensionGrade,
    calculateTotalScore,
    calculateTotalGrade,
    scoreToGrade,
    selectService,
    selectRole,
    selectStudent,
    setViewingScores,
    setCurrentDimension,
    setCurrentStep,
    resetSelection,
    resetRoleAndStudentSelection,
    setQueryParams,
    initializeFromStorage,
  }
})
