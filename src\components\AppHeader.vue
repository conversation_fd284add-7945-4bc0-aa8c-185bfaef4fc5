<template>
  <header class="bg-primary text-white shadow-md">
    <div class="container mx-auto px-4">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <i class="fa fa-plane text-2xl mr-3 airplane-animation"></i>
          <h1 class="text-xl md:text-2xl font-bold !mb-0">
            客舱设施与服务五力评价系统
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <div v-if="authStore.isAuthenticated" class="relative">
            <a-dropdown>
              <a
                class="ant-dropdown-link flex items-center cursor-pointer"
                @click.prevent
              >
                <UserOutlined class="mr-2" />
                <span>{{ authStore.userInfo?.nickName || '用户' }}</span>
                <DownOutlined class="ml-3 text-sm" />
              </a>
              <template #overlay>
                <a-menu @click="handleMenuClick">
                  <a-menu-item key="profile" class="!text-sm">
                    <template #icon>
                      <UserOutlined />
                    </template>
                    个人中心
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout" class="!text-sm">
                    <template #icon>
                      <LogoutOutlined />
                    </template>
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>

          <div v-else>
            <a-button type="link" @click="showLoginDialog" class="!text-white hover:!text-white/80">登录</a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录对话框 -->
    <LoginDialog v-model:open="loginDialogVisible" @success="handleLoginSuccess" />

    <!-- 个人信息对话框 -->
    <a-modal
      v-model:open="profileDialogVisible"
      title="个人信息"
      :footer="null"
      width="600px"
      centered
    >
      <div v-if="authStore.userInfo" class="profile-content">
        <!-- 用户头像和基本信息 -->
        <div class="flex items-center mb-6 p-4 bg-gray-50 rounded-lg">
          <div class="flex-shrink-0 mr-4">
            <a-avatar
              :size="80"
              :src="authStore.userInfo.avatar"
              class="bg-blue-500"
            >
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
          </div>
          <div class="flex-1">
            <h3 class="text-xl font-semibold mb-1">{{ authStore.userInfo.nickName }}</h3>
            <p class="text-gray-600 mb-1">@{{ authStore.userInfo.userName }}</p>
            <a-tag color="blue" v-if="authStore.userInfo.roles && authStore.userInfo.roles.length > 0">
              {{ authStore.userInfo.roles[0].roleName }}
            </a-tag>
          </div>
        </div>

        <!-- 详细信息 -->
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="用户名">
            {{ authStore.userInfo.userName }}
          </a-descriptions-item>
          <a-descriptions-item label="昵称">
            {{ authStore.userInfo.nickName }}
          </a-descriptions-item>
          <a-descriptions-item label="邮箱">
            {{ authStore.userInfo.email || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="手机号">
            {{ authStore.userInfo.phonenumber || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="性别">
            {{ authStore.userInfo.sex === '1' ? '男' : authStore.userInfo.sex === '0' ? '女' : '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="authStore.userInfo.status === '0' ? 'green' : 'red'">
              {{ authStore.userInfo.status === '0' ? '正常' : '停用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="部门" :span="2">
            {{ authStore.userInfo.dept?.deptName || '未分配' }}
          </a-descriptions-item>
          <a-descriptions-item label="角色" :span="2">
            <a-space>
              <a-tag
                v-for="role in authStore.userInfo.roles"
                :key="role.roleId"
                :color="role.admin ? 'red' : 'blue'"
              >
                {{ role.roleName }}
              </a-tag>
            </a-space>
          </a-descriptions-item>
          <a-descriptions-item label="最后登录IP">
            {{ authStore.userInfo.loginIp || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="最后登录时间">
            {{ formatDate(authStore.userInfo.loginDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="2">
            {{ formatDate(authStore.userInfo.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ authStore.userInfo.remark || '无' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 操作按钮 -->
        <div class="flex justify-end mt-6 space-x-2 gap-2">
          <a-button @click="profileDialogVisible = false">
            关闭
          </a-button>
          <!-- <a-button type="primary" @click="handleEditProfile">
            编辑资料
          </a-button> -->
        </div>
      </div>
    </a-modal>
  </header>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import LoginDialog from '@/components/auth/LoginDialog.vue'
import {
  UserOutlined,
  LogoutOutlined,
  DownOutlined,
} from '@ant-design/icons-vue'
import {
  Dropdown as ADropdown,
  Menu as AMenu,
  MenuItem as AMenuItem,
  MenuDivider as AMenuDivider,
  Button as AButton,
  Modal as AModal,
  Avatar as AAvatar,
  Tag as ATag,
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
  Space as ASpace,
} from 'ant-design-vue'

const authStore = useAuthStore()

const loginDialogVisible = ref(false)
const profileDialogVisible = ref(false)

const showLoginDialog = () => {
  loginDialogVisible.value = true
}

const handleLoginSuccess = () => {
  // 可选：登录成功后的额外操作，例如重新获取页面数据
  // 目前对话框自己会关闭，这里可以留空或用于其他逻辑
}

const handleMenuClick = ({ key }) => {
  switch (key) {
    case 'profile':
      profileDialogVisible.value = true
      break
    case 'logout':
      authStore.logout()
      break
  }
}

// const handleEditProfile = () => {
//   // 这里可以添加编辑个人资料的逻辑
//   // 比如跳转到编辑页面或打开编辑对话框
//   console.log('编辑个人资料')
// }

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch {
    return '格式错误'
  }
}
</script>

<style scoped>
.bg-primary {
  background-color: #0f3460;
}
</style>
