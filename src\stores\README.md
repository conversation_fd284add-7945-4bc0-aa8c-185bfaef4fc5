# 状态管理模块 (Stores)

该目录包含基于Pinia的状态管理，负责应用的全局状态管理和业务逻辑处理。

## 📁 文件结构

```
src/stores/
├── auth.js           # 用户认证状态管理
├── evaluation.js     # 评价系统状态管理
└── README.md         # 状态管理模块文档
```

## 🏗️ 状态管理架构

### 1. Pinia架构
- 使用 **Pinia** 作为状态管理库
- 支持 **Composition API** 和 **Options API** 两种写法
- 提供类型安全的状态管理
- 支持开发工具和热重载

### 2. Store分层设计
```
应用状态层
├── 认证状态 (auth)
│   ├── 用户信息
│   ├── 登录状态
│   └── 权限管理
└── 业务状态 (evaluation)
    ├── 学生数据
    ├── 评价数据
    ├── 界面状态
    └── 业务逻辑
```

### 3. 数据流设计
```
组件 → Action → API → State → 组件
  ↑                           ↓
  ←←←←← Getter/Computed ←←←←←←←
```

## 📄 Store详解

### `auth.js` - 用户认证Store

**功能**: 管理用户认证状态、登录登出和用户信息

#### State状态
```javascript
state: () => ({
  token: localStorage.getItem('token') || '',  // 认证令牌
  userInfo: null                               // 用户信息
})
```

#### Getters计算属性
```javascript
getters: {
  isAuthenticated: (state) => !!state.token,  // 是否已认证
  user: (state) => state.userInfo              // 用户信息
}
```

#### Actions操作
- `login(loginData)` - 用户登录
- `logout()` - 用户登出
- `getProfile()` - 获取用户信息

**使用示例**:
```javascript
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 登录
await authStore.login({ username: 'admin', password: '123456' })

// 检查认证状态
if (authStore.isAuthenticated) {
  console.log('用户已登录:', authStore.user)
}

// 登出
authStore.logout()
```

### `evaluation.js` - 评价系统Store

**功能**: 管理评价系统的核心业务逻辑和数据状态

#### State状态
```javascript
const students = ref([])              // 学生列表
const total = ref(0)                  // 学生总数
const queryParams = ref({             // 查询参数
  pageNum: 1,
  pageSize: 10,
  deptId: 201
})
const selectedRole = ref(null)        // 选中的评价身份
const selectedStudentId = ref(null)   // 选中的学生ID
const isViewingScores = ref(false)    // 是否在查看成绩
const currentDimension = ref(0)       // 当前评价维度
const marks = ref([])                 // 成绩数据
```

#### Computed计算属性
```javascript
const selectedStudent = computed(() => {
  // 根据ID查找选中的学生
  return students.value.find(s => s.id === selectedStudentId.value) || null
})

const evaluationProgress = computed(() => {
  // 计算评价进度
  const role = selectedRole.value
  const studentMarks = marks.value.filter(m => m.identity === role)
  // 返回完成的维度数量和总维度数
})

const canProceedToNext = computed(() => {
  // 判断是否可以进入下一个维度
})
```

#### Actions操作
- `fetchStudents()` - 获取学生列表
- `fetchMarks()` - 获取学生成绩
- `saveScore(dimensionId, indicatorId, score)` - 保存评分
- `setSelectedRole(role)` - 设置选中身份
- `setSelectedStudent(studentId)` - 设置选中学生
- `nextDimension()` - 进入下一维度
- `previousDimension()` - 返回上一维度
- `resetEvaluation()` - 重置评价状态

## 🚀 使用指南

### 1. 在组件中使用Store

```vue
<script setup>
import { useAuthStore } from '@/stores/auth'
import { useEvaluationStore } from '@/stores/evaluation'
import { computed, onMounted } from 'vue'

// 获取store实例
const authStore = useAuthStore()
const evaluationStore = useEvaluationStore()

// 使用计算属性
const isLoggedIn = computed(() => authStore.isAuthenticated)
const currentStudent = computed(() => evaluationStore.selectedStudent)

// 使用actions
const handleLogin = async (loginData) => {
  try {
    await authStore.login(loginData)
    // 登录成功后的逻辑
  } catch (error) {
    console.error('登录失败:', error)
  }
}

const selectStudent = (studentId) => {
  evaluationStore.setSelectedStudent(studentId)
}

// 生命周期中调用
onMounted(() => {
  evaluationStore.fetchStudents()
})
</script>

<template>
  <div>
    <!-- 使用状态 -->
    <div v-if="isLoggedIn">
      <p>欢迎, {{ authStore.user?.nickName }}</p>
      <button @click="authStore.logout">登出</button>
    </div>
    
    <!-- 使用评价状态 -->
    <div v-if="currentStudent">
      <h3>当前评价学生: {{ currentStudent.name }}</h3>
      <p>进度: {{ evaluationStore.evaluationProgress.completed }}/{{ evaluationStore.evaluationProgress.total }}</p>
    </div>
  </div>
</template>
```

### 2. 在其他Store中使用

```javascript
import { defineStore } from 'pinia'
import { useAuthStore } from './auth'

export const useOtherStore = defineStore('other', () => {
  const authStore = useAuthStore()
  
  const someAction = () => {
    if (authStore.isAuthenticated) {
      // 执行需要认证的操作
    }
  }
  
  return { someAction }
})
```

### 3. 状态持久化

```javascript
// 在auth store中
const login = async (loginData) => {
  const response = await loginApi(loginData)
  if (response.token) {
    token.value = response.token
    localStorage.setItem('token', response.token) // 持久化token
  }
}

const logout = () => {
  token.value = ''
  userInfo.value = null
  localStorage.removeItem('token') // 清除持久化数据
}
```

## 🔄 状态同步

### 1. 跨组件状态共享

```javascript
// 在多个组件中共享评价状态
// 组件A
const evaluationStore = useEvaluationStore()
evaluationStore.setSelectedRole('teacher')

// 组件B（自动响应状态变化）
const selectedRole = computed(() => evaluationStore.selectedRole)
watch(selectedRole, (newRole) => {
  console.log('身份变更为:', newRole)
})
```

### 2. 状态重置

```javascript
// 重置评价状态
const resetEvaluation = () => {
  selectedRole.value = null
  selectedStudentId.value = null
  currentDimension.value = 0
  isViewingScores.value = false
  marks.value = []
}
```

## 📊 数据管理

### 1. API集成

```javascript
// 在store中集成API调用
const fetchStudents = async () => {
  try {
    const response = await listUser(queryParams.value)
    students.value = response.rows.map(s => ({
      id: s.userName,
      name: s.nickName
    }))
    total.value = response.total
  } catch (error) {
    console.error('获取学生列表失败:', error)
  }
}
```

### 2. 错误处理

```javascript
const saveScore = async (dimensionId, indicatorId, score) => {
  try {
    const markData = {
      num: Number(selectedStudent.value.id),
      name: selectedStudent.value.name,
      dimensionality: getDimensionName(dimensionId),
      service: indicatorId,
      identity: selectedRole.value,
      mark: score.toString()
    }
    
    await addMarks(markData)
    await fetchMarks() // 重新获取数据
  } catch (error) {
    console.error('保存评分失败:', error)
    throw error // 重新抛出错误供组件处理
  }
}
```

### 3. 数据验证

```javascript
const setSelectedStudent = (studentId) => {
  const student = students.value.find(s => s.id === studentId)
  if (!student) {
    console.warn('无效的学生ID:', studentId)
    return
  }
  selectedStudentId.value = studentId
}
```

## 🔧 开发工具

### 1. Pinia DevTools
- 支持时间旅行调试
- 状态变化追踪
- Action执行记录

### 2. 类型支持

```javascript
/**
 * @typedef {import('@/types').Role} Role
 * @typedef {import('@/types').Student} Student
 */

/** @type {import('vue').Ref<Student[]>} */
const students = ref([])
```

## 🔗 相关文件

- [`@/api/`](../api/) - API接口调用
- [`@/types/index.js`](../types/index.js) - 类型定义
- [`@/constants/data.js`](../constants/data.js) - 常量数据
- [`src/main.js`](../main.js) - Pinia实例创建

## 📝 开发规范

### 1. Store命名规范
- **文件名**: 使用camelCase，如 `auth.js`
- **Store名**: 使用camelCase，如 `useAuthStore`
- **状态名**: 使用camelCase，描述性命名

### 2. 状态设计原则
- **单一职责**: 每个store负责特定的业务领域
- **最小化状态**: 只存储必要的状态，其他通过computed计算
- **不可变性**: 通过actions修改状态，避免直接修改

### 3. 异步处理
- **错误处理**: 在actions中处理API错误
- **加载状态**: 提供loading状态管理
- **数据同步**: 确保状态与后端数据同步

### 4. 性能优化
- **按需导入**: 只导入需要的store
- **计算属性缓存**: 合理使用computed缓存计算结果
- **状态分割**: 避免单个store过于庞大
