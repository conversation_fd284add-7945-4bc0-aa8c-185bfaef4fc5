# 仪表板模块 (Dashboard)

该目录包含航空评价系统的仪表板相关页面组件，提供数据可视化和分析功能。

## 📁 文件结构

```
src/views/dashboard/
├── README.md                           # 仪表板模块文档
├── OverallAnalysisDashboardView.vue    # 总评分析大屏
└── SingleTaskDashboardView.vue         # 单任务分析大屏
```

## 🎯 功能概述

### OverallAnalysisDashboardView.vue - 总评分析大屏

**功能**: 提供全面的学生评价数据分析和可视化展示

**主要特性**:
- 🔢 **数据统计卡片**: 显示评价任务数、参评学生数、综合评分、评力提升率
- 📈 **学生八任务成长趋势**: 折线图展示学生在8个服务中的五力发展轨迹
- 🎯 **全班五力综合分析**: 雷达图对比两个服务的全班五力平均水平
- 📊 **八任务能力培养效果对比**: 柱状图展示学生在各服务各维度的能力分布

**数据源**:
- `/prod-api/system/dict/data/type/hk_service` - 8个服务数据
- `/prod-api/hk/indicator/list` - 各服务指标数据
- `/prod-api/hk/marks/list` - 学生评分数据
- `/prod-api/system/user/list?deptId=201` - 学生列表

**核心算法**:
```javascript
// 五力评价权重计算
学生最终得分 = 学生评价 × 10% + 教师评价 × 50% + 企业评价 × 40%

// 等级转换
A = 100分, B = 80分, C = 60分, D = 40分, E = 20分
```

### SingleTaskDashboardView.vue - 单任务分析大屏

**功能**: 针对单个服务任务的详细分析和班级排名

**主要特性**:
- 📋 **班级排名表格**: 显示全班学生在选定服务中的五力得分排名
- 🎛️ **服务选择器**: 支持切换不同服务查看对应数据
- 📊 **实时数据更新**: 服务切换时自动刷新排名数据

## 🔧 技术实现

### 数据流架构

```mermaid
graph TD
    A[用户交互] --> B[组件状态更新]
    B --> C[API数据获取]
    C --> D[数据处理计算]
    D --> E[图表/表格渲染]
    E --> F[用户界面展示]
```

### 关键技术栈

- **Vue 3 Composition API**: 响应式数据管理
- **ECharts**: 数据可视化图表库
- **Ant Design Vue**: UI组件库
- **CSS Grid/Flexbox**: 响应式布局

### 数据处理流程

1. **数据获取**: 并行获取服务、学生、指标、评分数据
2. **数据清洗**: 过滤无效数据，统一数据格式
3. **分数计算**: 按权重计算加权平均分
4. **等级转换**: 数值分数转换为ABCDE等级
5. **图表渲染**: 将处理后数据传递给ECharts组件

## 🎨 设计规范

### 色彩系统

- **主色调**: 深蓝色背景 (#0a1929)
- **强调色**: 青蓝色 (#00d4ff)
- **维度色彩**:
  - 管理力: #ff6b6b (红色)
  - 执行力: #4ecdc4 (青色)
  - 观察力: #45b7d1 (蓝色)
  - 沟通力: #96ceb4 (绿色)
  - 应变力: #feca57 (黄色)

### 布局规范

- **网格系统**: CSS Grid 2×2 布局
- **响应式断点**: 1200px, 768px
- **间距标准**: 20px, 16px, 12px
- **圆角规范**: 16px (卡片), 8px (按钮)

## 📊 数据模型

### 学生评分数据结构

```javascript
{
  id: 1,                    // 评分ID
  num: "235601101",         // 学号
  name: "张三",             // 姓名
  service: "客舱服务",       // 服务项目
  dimensionality: "管理力",  // 评价维度
  identity: "教师",         // 评价身份
  ratingCriteriaMark: "B",  // 评分等级
  srcNun: "235601101",      // 评价者学号
  srcNane: "张三"           // 评价者姓名
}
```

### 服务数据结构

```javascript
{
  dictValue: "cabin_service",  // 服务标识
  dictLabel: "客舱服务",       // 服务名称
  dictSort: 1                  // 排序
}
```

## 🚀 使用指南

### 页面访问

- **总评分析**: `/dashboard/overall` - 管理员、教师、企业用户
- **单任务分析**: `/dashboard/single` - 所有用户类型

### 权限控制

```javascript
// 用户权限验证
const userInfo = JSON.parse(localStorage.getItem('userInfo'))
const allowedDeptIds = [101, 202, 203] // 管理员、教师、企业

if (!allowedDeptIds.includes(userInfo.user.deptId)) {
  // 重定向到首页或显示权限不足
}
```

### 数据刷新

- **自动刷新**: 30秒间隔自动更新数据
- **手动刷新**: 点击刷新按钮立即更新
- **切换刷新**: 学生/服务选择变化时自动刷新

## 🔍 调试指南

### 常见问题

1. **图表不显示**: 检查容器高度设置
2. **数据为空**: 检查API接口返回和权限
3. **计算错误**: 检查权重配置和数据过滤

### 调试工具

```javascript
// 开启详细日志
console.log('数据加载:', { services, students, marks })
console.log('计算结果:', { averages, rankings })
```

## 📈 性能优化

### 数据缓存

- 服务列表缓存 (不常变化)
- 学生列表缓存 (按学期更新)
- 评分数据实时获取

### 渲染优化

- 图表懒加载
- 数据分页处理
- 防抖处理用户交互

## 🔄 更新日志

### v1.0.0 (2025-01-16)
- ✅ 完成总评分析大屏基础功能
- ✅ 实现学生成长趋势图
- ✅ 添加全班五力雷达图
- ✅ 完成八任务柱状图对比
- ✅ 支持服务对比功能
- ✅ 优化空数据处理
- ✅ 修复图表标题重复问题

### 待开发功能
- 🔲 单任务分析大屏数据接入
- 🔲 导出功能
- 🔲 打印功能
- 🔲 更多图表类型支持

## 📞 技术支持

如有问题请联系开发团队或查看相关API文档。
