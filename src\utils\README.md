# 工具模块 (Utils)

该目录包含应用通用的工具函数和实用程序。

## 📁 文件结构

```
src/utils/
├── request.js        # HTTP请求工具
└── README.md         # 工具模块文档
```

## 🔧 设计原则

### 1. 统一的HTTP请求处理
- 集中配置Axios实例
- 统一的请求和响应拦截器
- 自动化的认证和错误处理

### 2. 可复用的工具函数
- 提供通用的业务逻辑处理
- 避免代码重复
- 便于维护和测试

### 3. 类型安全和文档化
- 完整的JSDoc注释
- 支持IDE智能提示
- 清晰的使用示例

## 📄 文件详解

### `request.js` - HTTP请求工具

**功能**: 提供统一配置的Axios实例，处理所有HTTP请求的通用逻辑

#### 实例配置

```javascript
const service = axios.create({
  baseURL: '/prod-api',                                    // API基础路径
  timeout: 10000,                                         // 请求超时时间(10秒)
  headers: { 'Content-Type': 'application/json;charset=utf-8' }  // 默认请求头
})
```

**配置说明**:
- **baseURL**: 设置为 `/prod-api`，通过Vite代理转发到后端服务
- **timeout**: 10秒超时，防止请求长时间挂起
- **headers**: 默认JSON内容类型，支持UTF-8编码

#### 请求拦截器

```javascript
service.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.isAuthenticated) {
      config.headers['Authorization'] = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    console.log(error)
    return Promise.reject(error)
  }
)
```

**功能**:
- **自动认证**: 检查用户认证状态，自动添加Authorization头
- **令牌格式**: 使用Bearer Token格式
- **错误处理**: 记录请求配置错误

#### 响应拦截器

```javascript
service.interceptors.response.use(
  (res) => {
    const { code, msg } = res.data
    if (code === 200) {
      return res.data
    } else {
      console.error('API Error: ', msg)
      return Promise.reject(new Error(msg || 'Error'))
    }
  },
  (error) => {
    console.log('err' + error)
    return Promise.reject(error)
  }
)
```

**功能**:
- **成功处理**: 检查业务状态码(200)，直接返回data字段
- **错误处理**: 统一处理API业务错误和网络错误
- **数据简化**: 自动提取响应数据，简化调用方代码

## 🚀 使用指南

### 1. 基础使用

```javascript
import request from '@/utils/request'

// GET请求
const getStudents = async () => {
  try {
    const response = await request({
      url: '/hk/students/list',
      method: 'get',
      params: { pageNum: 1, pageSize: 10 }
    })
    return response
  } catch (error) {
    console.error('获取学生列表失败:', error)
    throw error
  }
}

// POST请求
const createStudent = async (studentData) => {
  return request({
    url: '/hk/students',
    method: 'post',
    data: studentData
  })
}

// PUT请求
const updateStudent = async (id, studentData) => {
  return request({
    url: `/hk/students/${id}`,
    method: 'put',
    data: studentData
  })
}

// DELETE请求
const deleteStudent = async (id) => {
  return request({
    url: `/hk/students/${id}`,
    method: 'delete'
  })
}
```

### 2. 在API模块中使用

```javascript
// src/api/evaluation.js
import request from '@/utils/request'

/**
 * 查询学生列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} API响应
 */
export function getStudentList(params) {
  return request({
    url: '/hk/students/list',
    method: 'get',
    params
  })
}

/**
 * 新增成绩
 * @param {Object} data - 成绩数据
 * @returns {Promise<Object>} API响应
 */
export function addMarks(data) {
  return request({
    url: '/hk/marks',
    method: 'post',
    data
  })
}
```

### 3. 在Store中使用

```javascript
// src/stores/evaluation.js
import { defineStore } from 'pinia'
import { listUser } from '@/api/user'

export const useEvaluationStore = defineStore('evaluation', () => {
  const students = ref([])

  const fetchStudents = async () => {
    try {
      const response = await listUser(queryParams.value)
      students.value = response.rows.map(s => ({
        id: s.userName,
        name: s.nickName
      }))
    } catch (error) {
      console.error('Failed to fetch students:', error)
      // 错误已经在request拦截器中处理
    }
  }

  return { students, fetchStudents }
})
```

### 4. 错误处理最佳实践

```javascript
// 在组件中处理API调用
<script setup>
import { ref } from 'vue'
import { getStudentList } from '@/api/evaluation'

const loading = ref(false)
const students = ref([])
const error = ref(null)

const loadStudents = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await getStudentList({ pageNum: 1, pageSize: 10 })
    students.value = response.rows
  } catch (err) {
    error.value = err.message
    console.error('加载学生列表失败:', err)
  } finally {
    loading.value = false
  }
}
</script>
```

## 🔧 配置和扩展

### 1. 修改基础配置

```javascript
// 如需修改基础配置，编辑request.js文件
const service = axios.create({
  baseURL: process.env.NODE_ENV === 'development' ? '/dev-api' : '/prod-api',
  timeout: 15000,  // 增加超时时间
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
    'X-Requested-With': 'XMLHttpRequest'  // 添加自定义头
  }
})
```

### 2. 扩展请求拦截器

```javascript
service.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()

    // 添加认证头
    if (authStore.isAuthenticated) {
      config.headers['Authorization'] = `Bearer ${authStore.token}`
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    // 添加时间戳
    config.headers['X-Timestamp'] = Date.now()

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)
```

### 3. 扩展响应拦截器

```javascript
service.interceptors.response.use(
  (res) => {
    const { code, msg, data } = res.data

    // 处理不同的业务状态码
    switch (code) {
      case 200:
        return res.data
      case 401:
        // 未授权，跳转到登录页
        const authStore = useAuthStore()
        authStore.logout()
        router.push('/login')
        break
      case 403:
        // 无权限
        console.error('无权限访问:', msg)
        break
      default:
        console.error('API Error:', msg)
    }

    return Promise.reject(new Error(msg || 'Unknown error'))
  },
  (error) => {
    // 网络错误处理
    if (error.code === 'ECONNABORTED') {
      console.error('请求超时')
    } else if (error.response?.status === 500) {
      console.error('服务器内部错误')
    }

    return Promise.reject(error)
  }
)
```

## 🔗 相关文件

- [`@/api/`](../api/) - 使用request实例的API模块
- [`@/stores/auth.js`](../stores/auth.js) - 提供认证状态的Store
- [`vite.config.js`](../../vite.config.js) - 代理配置
- [`@/types/index.js`](../types/index.js) - 相关类型定义

## 📝 开发规范

### 1. 请求配置规范
- **URL路径**: 使用相对路径，依赖baseURL配置
- **方法名**: 使用小写字符串，如 'get', 'post'
- **参数传递**: GET请求使用params，POST/PUT使用data

### 2. 错误处理规范
- **统一处理**: 在拦截器中处理通用错误
- **业务错误**: 在调用方处理特定业务逻辑错误
- **用户提示**: 提供友好的错误提示信息

### 3. 性能优化
- **请求去重**: 避免重复的API调用
- **缓存策略**: 合理使用缓存减少请求
- **超时设置**: 根据接口特性设置合适的超时时间

### 4. 安全考虑
- **令牌管理**: 安全存储和传输认证令牌
- **HTTPS**: 生产环境使用HTTPS协议
- **输入验证**: 在发送请求前验证参数