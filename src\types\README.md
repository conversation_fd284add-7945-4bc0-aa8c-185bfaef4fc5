# 类型定义模块 (Types)

该目录包含项目的所有TypeScript类型定义，使用JSDoc提供完整的类型支持和IDE智能提示。

## 📁 文件结构

```
src/types/
├── index.js          # 主要类型定义文件
└── README.md         # 类型模块文档
```

## 🎯 设计原则

### 1. 类型安全
- 使用JSDoc提供完整的类型定义
- 支持IDE智能提示和类型检查
- 减少运行时类型错误

### 2. 统一管理
- 集中定义所有项目类型
- 避免类型定义分散和重复
- 便于维护和更新

### 3. 文档化
- 每个类型都有详细的属性说明
- 提供使用示例和注意事项
- 支持自动生成API文档

## 📄 类型分类

### `index.js` - 主要类型定义

**功能**: 定义项目中所有数据结构的类型，包括业务数据、API接口和系统配置

#### 1. 核心业务类型

**学生信息类型**
```javascript
/**
 * 学生信息类型
 * @typedef {Object} Student
 * @property {string} id - 学生ID（学号）
 * @property {string} name - 学生姓名
 */
```

**评价身份类型**
```javascript
/**
 * 评价身份类型
 * @typedef {'student' | 'teacher' | 'enterprise'} Role
 */
```

**评价指标类型**
```javascript
/**
 * 评价指标类型
 * @typedef {Object} Indicator
 * @property {string} id - 指标ID
 * @property {string} name - 指标名称
 * @property {string} criteria - 评价标准
 */
```

**评价维度类型**
```javascript
/**
 * 评价维度类型
 * @typedef {Object} Dimension
 * @property {string} id - 维度ID
 * @property {string} name - 维度名称
 * @property {Indicator[]} indicators - 指标列表
 */
```

#### 2. 评分数据类型

**单个维度评分数据**
```javascript
/**
 * 单个维度评分数据
 * @typedef {Object} DimensionScore
 * @property {boolean} completed - 是否完成评分
 * @property {number} [indicatorId] - 指标评分（动态属性）
 */
```

**角色评分数据**
```javascript
/**
 * 角色评分数据
 * @typedef {Object} RoleScores
 * @property {DimensionScore} management - 管理维度评分
 * @property {DimensionScore} execution - 执行维度评分
 * @property {DimensionScore} observation - 观察维度评分
 * @property {DimensionScore} communication - 沟通维度评分
 * @property {DimensionScore} adaptability - 适应性维度评分
 */
```

**学生完整评价数据**
```javascript
/**
 * 学生完整评价数据
 * @typedef {Object} StudentEvaluationData
 * @property {string} name - 学生姓名
 * @property {Object} scores - 各角色评分
 * @property {RoleScores} scores.student - 学生评分
 * @property {RoleScores} scores.teacher - 教师评分
 * @property {RoleScores} scores.enterprise - 企业评分
 * @property {Object} status - 各角色评价状态
 * @property {string} status.student - 学生评价状态
 * @property {string} status.teacher - 教师评价状态
 * @property {string} status.enterprise - 企业评价状态
 */
```

#### 3. 应用状态类型

**全局状态类型**
```javascript
/**
 * 全局状态类型
 * @typedef {Object} AppState
 * @property {number} currentPage - 当前页面
 * @property {Role|null} selectedRole - 选中的角色
 * @property {string|null} selectedStudentId - 选中的学生ID
 * @property {boolean} isViewingScores - 是否正在查看分数
 * @property {number} currentDimension - 当前维度
 * @property {Record<string, StudentEvaluationData>} evaluationData - 评价数据
 */
```

**身份权重配置**
```javascript
/**
 * 身份权重配置
 * @typedef {Object} RoleWeight
 * @property {Role} role - 角色类型
 * @property {string} name - 角色名称
 * @property {number} weight - 权重
 * @property {string} icon - 图标
 */
```

#### 4. API接口类型

**通用API响应格式**
```javascript
/**
 * 通用API响应格式
 * @template T
 * @typedef {Object} ApiResponse
 * @property {number} total - 总数
 * @property {T[]} rows - 数据行
 * @property {number} code - 响应码
 * @property {string} msg - 响应消息
 */
```

**登录相关类型**
```javascript
/**
 * 登录数据
 * @typedef {Object} LoginData
 * @property {string} [username] - 用户名
 * @property {string} [password] - 密码
 * @property {string} [code] - 验证码
 * @property {string} [uuid] - UUID
 */

/**
 * 登录响应
 * @typedef {Object} LoginResponse
 * @property {number} code - 响应码
 * @property {string} msg - 响应消息
 * @property {string} token - 访问令牌
 */
```

**用户信息类型**
```javascript
/**
 * 用户数据
 * @typedef {Object} UserData
 * @property {string} createBy - 创建者
 * @property {string} createTime - 创建时间
 * @property {string|null} updateBy - 更新者
 * @property {string|null} updateTime - 更新时间
 * @property {string} remark - 备注
 * @property {Record<string, any>} params - 参数
 * @property {number} userId - 用户ID
 * @property {number} deptId - 部门ID
 * @property {string} userName - 用户名
 * @property {string} nickName - 昵称
 * @property {string} email - 邮箱
 * @property {string} phonenumber - 手机号
 * @property {string} sex - 性别
 * @property {string|null} avatar - 头像
 * @property {string} status - 状态
 * @property {string} delFlag - 删除标志
 * @property {string} loginIp - 登录IP
 * @property {string} loginDate - 登录日期
 * @property {string} pwdUpdateDate - 密码更新日期
 * @property {Dept} dept - 部门信息
 * @property {RoleInfo[]} roles - 角色列表
 * @property {number[]|null} roleIds - 角色ID列表
 * @property {number[]|null} postIds - 岗位ID列表
 * @property {number|null} roleId - 角色ID
 * @property {boolean} admin - 是否管理员
 */
```

#### 5. 图表数据类型

**图表数据类型**
```javascript
/**
 * 图表数据类型
 * @typedef {Object} ChartData
 * @property {string[]} labels - 标签数组
 * @property {any[]} datasets - 数据集数组
 */
```

## 🚀 使用指南

### 1. 在JavaScript文件中使用

```javascript
/**
 * 处理学生数据的函数
 * @param {Student[]} students - 学生列表
 * @param {Role} role - 评价身份
 * @returns {StudentEvaluationData[]} 评价数据
 */
function processStudentData(students, role) {
  return students.map(student => ({
    name: student.name,
    scores: initializeScores(),
    status: {
      student: '未评价',
      teacher: '未评价',
      enterprise: '未评价'
    }
  }))
}

/**
 * API调用函数
 * @param {LoginData} loginData - 登录数据
 * @returns {Promise<LoginResponse>} 登录响应
 */
async function login(loginData) {
  const response = await fetch('/api/login', {
    method: 'POST',
    body: JSON.stringify(loginData)
  })
  return response.json()
}
```

### 2. 在Vue组件中使用

```vue
<script setup>
/**
 * @typedef {import('@/types').Student} Student
 * @typedef {import('@/types').Role} Role
 */

import { ref, computed } from 'vue'

/** @type {import('vue').Ref<Student[]>} */
const students = ref([])

/** @type {import('vue').Ref<Role | null>} */
const selectedRole = ref(null)

/**
 * 选择学生
 * @param {Student} student - 学生对象
 */
const selectStudent = (student) => {
  console.log('选择学生:', student.name)
}

/**
 * 计算属性 - 过滤后的学生列表
 * @type {import('vue').ComputedRef<Student[]>}
 */
const filteredStudents = computed(() => {
  return students.value.filter(student => 
    student.name.includes(searchQuery.value)
  )
})
</script>
```

### 3. 在Store中使用

```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * @typedef {import('@/types').Student} Student
 * @typedef {import('@/types').Role} Role
 * @typedef {import('@/types').StudentEvaluationData} StudentEvaluationData
 */

export const useEvaluationStore = defineStore('evaluation', () => {
  /** @type {import('vue').Ref<Student[]>} */
  const students = ref([])
  
  /** @type {import('vue').Ref<Role | null>} */
  const selectedRole = ref(null)
  
  /** @type {import('vue').Ref<Record<string, StudentEvaluationData>>} */
  const evaluationData = ref({})
  
  /**
   * 设置选中的角色
   * @param {Role} role - 角色类型
   */
  const setSelectedRole = (role) => {
    selectedRole.value = role
  }
  
  return {
    students,
    selectedRole,
    evaluationData,
    setSelectedRole
  }
})
```

### 4. 在API文件中使用

```javascript
import request from '@/utils/request'

/**
 * 获取学生列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 页面大小
 * @returns {Promise<import('@/types').ApiResponse<import('@/types').Student>>} 学生列表响应
 */
export function getStudentList(params) {
  return request({
    url: '/hk/students/list',
    method: 'get',
    params
  })
}

/**
 * 用户登录
 * @param {import('@/types').LoginData} data - 登录数据
 * @returns {Promise<import('@/types').LoginResponse>} 登录响应
 */
export function login(data) {
  return request({
    url: '/login',
    method: 'post',
    data
  })
}
```

## 🔧 类型扩展

### 1. 添加新的业务类型

```javascript
/**
 * 新的业务实体类型
 * @typedef {Object} NewEntity
 * @property {string} id - 实体ID
 * @property {string} name - 实体名称
 * @property {string} description - 描述
 * @property {Date} createdAt - 创建时间
 * @property {Date} updatedAt - 更新时间
 */
```

### 2. 扩展现有类型

```javascript
/**
 * 扩展的学生信息类型
 * @typedef {Student & {
 *   class: string,
 *   major: string,
 *   grade: number
 * }} ExtendedStudent
 */
```

### 3. 泛型类型定义

```javascript
/**
 * 分页响应类型
 * @template T
 * @typedef {Object} PaginatedResponse
 * @property {T[]} data - 数据列表
 * @property {number} total - 总数
 * @property {number} page - 当前页
 * @property {number} pageSize - 页面大小
 */
```

## 🔗 相关文件

- [`@/api/`](../api/) - 使用类型定义的API接口
- [`@/stores/`](../stores/) - 使用类型定义的状态管理
- [`@/constants/data.js`](../constants/data.js) - 相关的常量数据
- [`@/components/`](../components/) - 使用类型的组件

## 📝 开发规范

### 1. 类型命名规范
- **类型名**: 使用PascalCase，如 `StudentData`
- **属性名**: 使用camelCase，如 `studentId`
- **枚举类型**: 使用字符串字面量联合类型

### 2. 文档规范
- **必须提供**: 每个属性的类型和描述
- **可选属性**: 使用 `[propertyName]` 标记
- **复杂类型**: 提供详细的结构说明

### 3. 类型组织
- **按功能分组**: 相关类型放在一起
- **依赖关系**: 被依赖的类型定义在前
- **导入导出**: 合理使用模块化组织

### 4. 最佳实践
- **类型复用**: 避免重复定义相似类型
- **类型安全**: 使用严格的类型定义
- **向后兼容**: 新增属性时考虑兼容性
- **文档同步**: 类型变更时同步更新文档
