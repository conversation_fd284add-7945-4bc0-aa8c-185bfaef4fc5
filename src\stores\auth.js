import { defineStore } from 'pinia'
import { login as login<PERSON>pi, getProfile as getProfile<PERSON><PERSON> } from '@/api/auth'

/**
 * @typedef {import('@/types').LoginData} LoginData
 * @typedef {import('@/types').UserData} UserData
 */

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    /** @type {UserData | null} */
    userInfo: null,
  }),
  getters: {
    isAuthenticated: (state) => !!state.token,
    // Optional: getter for user info
    user: (state) => state.userInfo,
  },
  actions: {
    async login(loginData) {
      try {
        const response = await loginApi(loginData)
        if (response.token) {
          this.token = response.token
          localStorage.setItem('token', response.token)
          // After login, get user profile
          await this.getProfile()
          
          // 检查是否有保存的重定向路径
          const redirectPath = sessionStorage.getItem('redirectAfterLogin')
          if (redirectPath) {
            sessionStorage.removeItem('redirectAfterLogin')
            // 使用 router 进行重定向
            const router = await import('@/router').then(m => m.default)
            router.push(redirectPath)
          }
        }
        return response
      } catch (error) {
        console.error('Login failed:', error)
        throw error
      }
    },
    logout() {
      this.token = ''
      this.userInfo = null
      localStorage.removeItem('token')
      // You may want to redirect to the login page or reload the app
      window.location.reload()
    },
    async getProfile() {
      if (!this.token) {
        return
      }
      try {
        const response = await getProfileApi()
        this.userInfo = response.data
      } catch (error) {
        // If getting profile fails (e.g., 401), it means the token is invalid.
        // So, we log out.
        console.error('Failed to get user profile, logging out:', error)
        this.logout()
        throw error
      }
    },
  },
})
