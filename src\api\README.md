# API 模块 (API)

该目录包含所有与后端API交互的接口函数，采用模块化设计，按功能领域分类组织。

## 📁 文件结构

```
src/api/
├── index.js          # 统一导出文件
├── auth.js           # 认证相关API
├── evaluation.js     # 评价系统API
├── marks.js          # 成绩管理API
└── user.js           # 用户管理API
```

## 🔧 设计原则

### 1. 模块化组织
- 按业务功能领域划分API文件
- 每个文件专注于特定的业务领域
- 通过 `index.js` 统一导出，简化导入

### 2. 统一的请求处理
- 所有API函数都使用 `@/utils/request` 中的配置化axios实例
- 自动处理认证token、错误处理和响应数据提取
- 支持请求拦截器和响应拦截器

### 3. 完整的类型注释
- 使用JSDoc提供完整的参数和返回值类型定义
- 支持IDE智能提示和类型检查
- 便于维护和理解API接口

## 📄 文件详解

### `index.js` - 统一导出
```javascript
export * from './evaluation'
export * from './auth'
export * from './marks'
```

**作用**: 作为"桶文件"(barrel file)，统一导出所有API函数，简化导入操作。

**使用示例**:
```javascript
// 可以从根模块直接导入
import { login, getStudentList, addMarks } from '@/api'

// 而不需要分别从各个文件导入
// import { login } from '@/api/auth'
// import { getStudentList } from '@/api/evaluation'
// import { addMarks } from '@/api/marks'
```

### `auth.js` - 认证相关API

**功能**: 处理用户认证相关的所有API请求

**主要接口**:
- `login(data)` - 用户登录
- `getProfile()` - 获取用户信息

**使用示例**:
```javascript
import { login, getProfile } from '@/api'

// 登录
const loginData = { username: 'admin', password: '123456' }
const response = await login(loginData)

// 获取用户信息
const userInfo = await getProfile()
```

### `evaluation.js` - 评价系统API

**功能**: 处理评价系统核心功能的API请求

**主要接口**:
- `getIndicatorList(params)` - 查询指标列表
- `getStudentList(params)` - 查询学生列表

**参数说明**:
- `params.pageNum` - 页码
- `params.pageSize` - 页面大小

**使用示例**:
```javascript
import { getIndicatorList, getStudentList } from '@/api'

// 获取指标列表
const indicators = await getIndicatorList({
  pageNum: 1,
  pageSize: 10
})

// 获取学生列表
const students = await getStudentList({
  pageNum: 1,
  pageSize: 20
})
```

### `marks.js` - 成绩管理API

**功能**: 处理学生成绩的CRUD操作

**主要接口**:
- `listMarks(params)` - 查询成绩列表
- `getMarks(id)` - 查询成绩详情
- `addMarks(data)` - 新增成绩
- `updateMarks(data)` - 修改成绩
- `delMarks(id)` - 删除成绩

**数据结构**:
```javascript
// 成绩对象结构
const marks = {
  id: 1,                    // 成绩ID
  num: 2023001,            // 学生编号
  name: '张三',            // 学生姓名
  dimensionality: '管理力', // 评价维度
  service: '客舱服务',      // 服务项目
  identity: 'teacher',      // 评价身份
  mark: '85',              // 分数
  createTime: '2025-01-09', // 创建时间
  remark: '表现良好'        // 备注
}
```

**使用示例**:
```javascript
import { listMarks, addMarks, updateMarks } from '@/api'

// 查询成绩列表
const marksList = await listMarks({
  pageNum: 1,
  pageSize: 10,
  num: 2023001  // 按学生编号筛选
})

// 新增成绩
const newMark = await addMarks({
  num: 2023001,
  name: '张三',
  dimensionality: '管理力',
  service: '客舱服务',
  identity: 'teacher',
  mark: '85'
})

// 更新成绩
const updatedMark = await updateMarks({
  id: 1,
  mark: '90'
})
```

### `user.js` - 用户管理API

**功能**: 处理用户管理相关的API请求

**主要接口**:
- `listUser(params)` - 查询用户列表

**使用示例**:
```javascript
import { listUser } from '@/api'

// 获取用户列表
const users = await listUser({
  pageNum: 1,
  pageSize: 10
})
```

## 🚀 使用指南

### 1. 导入API函数
```javascript
// 推荐：从根模块导入
import { login, getStudentList, addMarks } from '@/api'

// 或者从具体文件导入
import { login } from '@/api/auth'
import { getStudentList } from '@/api/evaluation'
```

### 2. 错误处理
API函数会自动处理HTTP错误，但业务逻辑错误需要在调用处处理：

```javascript
try {
  const result = await login(loginData)
  // 处理成功响应
} catch (error) {
  // 错误已经在request拦截器中处理
  // 这里可以处理特定的业务逻辑
  console.error('登录失败:', error)
}
```

### 3. 在Store中使用
```javascript
// stores/auth.js
import { login, getProfile } from '@/api'

export const useAuthStore = defineStore('auth', () => {
  const userInfo = ref(null)
  
  const loginAction = async (loginData) => {
    const response = await login(loginData)
    userInfo.value = response.user
    return response
  }
  
  return { userInfo, loginAction }
})
```

### 4. 在组件中使用
```javascript
// 在Vue组件中
<script setup>
import { getStudentList } from '@/api'
import { ref, onMounted } from 'vue'

const students = ref([])
const loading = ref(false)

const fetchStudents = async () => {
  loading.value = true
  try {
    const response = await getStudentList({ pageNum: 1, pageSize: 20 })
    students.value = response.rows
  } finally {
    loading.value = false
  }
}

onMounted(fetchStudents)
</script>
```

## 🔗 相关文件

- [`@/utils/request.js`](../utils/request.js) - HTTP请求配置和拦截器
- [`@/types/index.js`](../types/index.js) - API相关的类型定义
- [`@/stores/`](../stores/) - 使用API的状态管理

## 📝 开发规范

### 1. 新增API函数
- 在对应的业务模块文件中添加函数
- 提供完整的JSDoc注释
- 在 `index.js` 中导出
- 更新相关类型定义

### 2. 命名规范
- 函数名使用动词开头，如 `getStudentList`、`addMarks`
- 参数对象使用 `params`（查询参数）或 `data`（请求体数据）
- 返回Promise，支持async/await

### 3. 错误处理
- 依赖 `@/utils/request` 的统一错误处理
- 不在API层处理业务逻辑错误
- 保持API函数的纯净性
