---
description: 
globs: 
alwaysApply: false
---
# 五力评价系统业务逻辑

## 评价模型
本系统采用"五力评价模型"，对学生的客舱服务能力进行全面评估。

### 五个核心维度
1. **管理力** - 流程管理和协调能力
   - 全流程完整性
   - 跨岗位协作
   - 文化资源整合
   - 时效管理

2. **执行力** - 操作执行和标准化能力
   - 动作标准化
   - 设备使用
   - 时效性
   - 细节处理

3. **观察力** - 需求识别和预判能力
   - 显性需求响应
   - 隐性需求洞察
   - 风险预判
   - 文化敏感度

4. **沟通力** - 交流沟通和表达能力
   - 语言策略
   - 非语言技巧
   - 方言应用
   - 禁忌规避

5. **应变力** - 问题处理和危机应对能力
   - 流程异常处理
   - 冲突化解
   - 极端案例应对
   - 文化冲突调解

## 评分算法

### 身份权重
- **学生评价**: 10% (学生自评或互评)
- **教师评价**: 50% (专业教师评价)
- **企业评价**: 40% (行业专家评价)

### 计算公式
```
维度平均分 = Σ(该维度4个指标分数) / 4
身份维度得分 = 维度平均分 × 身份权重
学生最终成绩 = Σ(各身份维度得分 × 维度权重)
```

其中：
- 每个指标评分范围：0-100分
- 维度权重：每个维度占总分的20%
- 最终成绩范围：0-100分

## 数据结构
评价数据存储在 [src/stores/evaluation.ts](mdc:src/stores/evaluation.ts) 中，使用 localStorage 进行持久化。

每个学生的完整评价数据包括：
- 基本信息（学号、姓名）
- 三种身份下的五维度评分
- 各身份的评价状态（未评价/进行中/已完成）

## 评分标准
每个指标都有详细的评分标准，分为5个等级：
- 80-100分：优秀
- 61-80分：良好  
- 41-60分：一般
- 21-40分：较差
- 0-20分：未完成/不合格

