import axios from 'axios'
import { useAuthStore } from '@/stores/auth'

// Create axios instance
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: { 'Content-Type': 'application/json;charset=utf-8' },
})

// Request interceptor
service.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.isAuthenticated) {
      config.headers['Authorization'] = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    console.log(error)
    return Promise.reject(error)
  },
)

// Response interceptor
service.interceptors.response.use(
  (res) => {
    const { code, msg } = res.data
    if (code === 200) {
      return res.data
    } else {
      // Handle errors
      console.error('API Error: ', msg)
      return Promise.reject(new Error(msg || 'Error'))
    }
  },
  (error) => {
    console.log('err' + error)
    return Promise.reject(error)
  },
)

export default service
