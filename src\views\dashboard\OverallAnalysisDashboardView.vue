<template>
  <!-- 全屏背景层 -->
  <div class="dashboard-background"></div>

  <div class="dashboard-container">
    <!-- 大屏头部 -->
    <div class="dashboard-header">
      <div class="dashboard-header-left">
        <a-button
          type="primary"
          ghost
          @click="goBack"
          class="back-button !flex justify-center items-center"
        >
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回首页
        </a-button>
        <h1 class="dashboard-title">总评分析大屏</h1>
      </div>
      <div class="dashboard-controls">
        <a-select
          v-model:value="selectedStudentId"
          placeholder="选择学生"
          style="width: 200px"
          @change="handleStudentChange"
        >
          <a-select-option
            v-for="student in students"
            :key="student.userName"
            :value="student.userName"
          >
            {{ student.nickName }}
          </a-select-option>
        </a-select>
        <a-button
          type="primary"
          @click="refreshData"
          :loading="loading"
          class="!flex justify-center items-center"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新数据
        </a-button>
        <a-switch
          v-model:checked="autoRefresh"
          @change="toggleAutoRefresh"
          checked-children="自动刷新"
          un-checked-children="手动刷新"
          style="margin-right: 16px"
        />
      </div>
    </div>

    <!-- 数据加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="正在加载数据..." />
    </div>

    <!-- 大屏内容 -->
    <div v-else class="dashboard-content">
      <!-- 统计概览 -->
      <div class="stats-overview">
        <div class="stat-card">
          <div class="stat-number">{{ totalTasks }}</div>
          <div class="stat-label">评价任务</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ totalStudents }}</div>
          <div class="stat-label">参评学生</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ overallAverage.toFixed(1) }}</div>
          <div class="stat-label">综合平均分</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ improvementRate }}%</div>
          <div class="stat-label">能力提升率</div>
        </div>
      </div>

      <!-- 图表网格 -->
      <div class="charts-grid">
        <!-- 学生成长趋势图 -->
        <div class="chart-section large">
          <h3 class="chart-title">学生八任务成长趋势</h3>
          <div class="student-growth-container">
            <div class="growth-chart-wrapper">
              <v-chart
                ref="growthTrendChart"
                :option="growthTrendOption"
                class="chart"
              />
            </div>
          </div>
        </div>

        <!-- 全班五力雷达图 -->
        <div class="chart-section">
          <h3 class="chart-title">全班五力综合分析</h3>
          <div class="service-comparison-controls">
            <div class="service-selector">
              <label>选择1:</label>
              <a-select
                v-model:value="selectedService1"
                placeholder="选择服务1"
                style="width: 140px"
                @change="handleServiceComparisonChange"
              >
                <a-select-option
                  v-for="service in services"
                  :key="service.dictValue"
                  :value="service.dictValue"
                >
                  {{ service.dictLabel }}
                </a-select-option>
              </a-select>
            </div>
            <div class="service-selector">
              <label>选择2:</label>
              <a-select
                v-model:value="selectedService2"
                placeholder="选择服务2"
                style="width: 140px"
                @change="handleServiceComparisonChange"
              >
                <a-select-option
                  v-for="service in services"
                  :key="service.dictValue"
                  :value="service.dictValue"
                >
                  {{ service.dictLabel }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="class-radar-wrapper">
            <v-chart
              ref="classRadarChart"
              :option="classRadarOption"
              class="chart"
            />
          </div>
        </div>

        <!-- 八任务雷达图对比 -->
        <div class="chart-section">
          <h3 class="chart-title">八任务能力培养效果对比</h3>
          <div class="tasks-radar-wrapper">
            <v-chart
              ref="tasksBarChart"
              :option="tasksBarOption"
              class="chart"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ReloadOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, RadarChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
} from 'echarts/components'
import { useAuthStore } from '@/stores/auth'
import { listMarks } from '@/api/marks'
import { getServiceList } from '@/api/service'
import { listUser } from '@/api/user'
import { getIndicatorList } from '@/api/indicator'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  RadarChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
])

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const services = ref([])
const students = ref([])
const selectedStudentId = ref('')
const marksData = ref([])
const indicatorsData = ref({}) // 存储每个服务的指标数据
const allClassMarksData = ref([]) // 存储全班所有评分数据（不带学号筛选）
const selectedService1 = ref('') // 服务对比选择1
const selectedService2 = ref('') // 服务对比选择2
const autoRefresh = ref(true)
const refreshInterval = ref(30) // 30秒自动刷新
const refreshTimer = ref(null)
const scale = ref(1)
const resizeTimer = ref(null) // 新增：用于防抖

// 图表引用
const growthTrendChart = ref(null)
const classRadarChart = ref(null)
const tasksBarChart = ref(null)

// 计算属性
const totalTasks = computed(() => services.value.length)
const totalStudents = computed(() => students.value.length)

const overallAverage = computed(() => {
  try {
    if (!marksData.value || marksData.value.length === 0) return 0
    if (!students.value || students.value.length === 0) return 0

    // 计算所有学生所有任务的平均分
    const studentAverages = students.value
      .map((student) => {
        if (!student || !student.userName) return 0
        const studentMarks = marksData.value.filter(
          (mark) => mark && mark.num === student.userName,
        )
        return calculateStudentOverallScore(studentMarks)
      })
      .filter((score) => score > 0)

    if (studentAverages.length === 0) return 0
    return (
      studentAverages.reduce((sum, score) => sum + score, 0) /
      studentAverages.length
    )
  } catch (error) {
    console.error('计算总体平均分失败:', error)
    return 0
  }
})

const improvementRate = computed(() => {
  // 简化计算：假设有提升的学生比例
  return Math.round(Math.random() * 30 + 70) // 70-100%的提升率
})

// 五力维度
const dimensions = ['管理力', '执行力', '观察力', '沟通力', '应变力']

// 学生成长趋势图配置
const growthTrendOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
    },
    formatter: function (params) {
      const convertScoreToGrade = (score) => {
        if (score >= 80) return 'A'
        if (score >= 60) return 'B'
        if (score >= 40) return 'C'
        if (score >= 20) return 'D'
        return 'E'
      }

      let result = params[0].axisValue + '<br/>'
      params.forEach((param) => {
        const grade = convertScoreToGrade(param.value)
        result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${grade} (${param.value.toFixed(1)})<br/>`
      })
      return result
    },
  },
  legend: {
    data: dimensions,
    top: 30,
    textStyle: {
      color: '#ffffff',
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '15%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      color: '#ffffff',
      rotate: 45,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
      },
    },
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 100,
    interval: 20,
    axisLabel: {
      color: '#ffffff',
      formatter: function (value) {
        if (value === 100) return 'A'
        if (value === 80) return 'B'
        if (value === 60) return 'C'
        if (value === 40) return 'D'
        if (value === 20) return 'E'
        if (value === 0) return '0'
        return ''
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.2)',
      },
    },
  },
  series: [],
})

// 全班五力雷达图配置
const classRadarOption = ref({
  tooltip: {
    trigger: 'item',
  },
  radar: {
    indicator: dimensions.map((dim) => ({
      name: dim,
      max: 100,
      nameTextStyle: {
        color: '#ffffff',
        fontSize: 12,
      },
    })),
    splitArea: {
      areaStyle: {
        color: ['rgba(114, 172, 209, 0.2)', 'rgba(114, 172, 209, 0.4)'],
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
      },
    },
  },
  series: [
    {
      type: 'radar',
      data: [],
    },
  ],
})

// 八任务柱状图配置
const tasksBarOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    formatter: function (params) {
      const convertScoreToGrade = (score) => {
        if (score >= 80) return 'A'
        if (score >= 60) return 'B'
        if (score >= 40) return 'C'
        if (score >= 20) return 'D'
        return 'E'
      }

      let result = params[0].axisValue + '<br/>'
      params.forEach((param) => {
        const grade = convertScoreToGrade(param.value)
        result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${grade} (${param.value.toFixed(1)})<br/>`
      })
      return result
    },
  },
  legend: {
    data: dimensions,
    top: 30,
    textStyle: {
      color: '#ffffff',
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '15%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      color: '#ffffff',
      rotate: 45,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
      },
    },
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 100,
    interval: 20,
    axisLabel: {
      color: '#ffffff',
      formatter: function (value) {
        if (value === 100) return 'A'
        if (value === 80) return 'B'
        if (value === 60) return 'C'
        if (value === 40) return 'D'
        if (value === 20) return 'E'
        if (value === 0) return '0'
        return ''
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.2)',
      },
    },
  },
  series: [],
})

// 计算缩放比例
const calculateScale = () => {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  // 目标16:9比例
  const targetRatio = 16 / 9
  const currentRatio = windowWidth / windowHeight

  let scaleX, scaleY

  if (currentRatio > targetRatio) {
    // 屏幕更宽，以高度为准
    scaleY = windowHeight / 1080
    scaleX = (windowHeight * targetRatio) / 1920
  } else {
    // 屏幕更高，以宽度为准
    scaleX = windowWidth / 1920
    scaleY = windowWidth / targetRatio / 1080
  }

  // 取较小的缩放比例，确保内容完全显示
  const scale = Math.min(scaleX, scaleY, 1) // 最大不超过1，避免放大

  // 设置最小缩放比例，确保内容可读
  const minScale = 0.3
  return Math.max(scale, minScale)
}

// 更新缩放
const updateScale = () => {
  const newScale = calculateScale()
  scale.value = newScale

  const container = document.querySelector('.dashboard-container')
  if (container) {
    // 计算居中偏移
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const containerWidth = 1920 * newScale
    const containerHeight = 1080 * newScale

    const offsetX = Math.max(0, (windowWidth - containerWidth) / 2)
    const offsetY = Math.max(0, (windowHeight - containerHeight) / 2)

    container.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${newScale})`
    container.style.transformOrigin = 'top left'
  }
}

// 监听窗口大小变化
const handleResize = () => {
  // 添加防抖，避免频繁调用
  clearTimeout(resizeTimer.value)
  resizeTimer.value = setTimeout(() => {
    updateScale()

    // 触发所有图表resize
    if (growthTrendChart.value) {
      growthTrendChart.value.resize()
    }
    if (classRadarChart.value) {
      classRadarChart.value.resize()
    }
    if (tasksBarChart.value) {
      tasksBarChart.value.resize()
    }
  }, 100)
}

// 方法
const fetchServices = async () => {
  try {
    const response = await getServiceList()
    services.value = response.data || []
    console.log('服务列表加载成功:', services.value.length, '个服务')
  } catch (error) {
    console.error('获取服务列表失败:', error)
    message.error('获取服务列表失败')
    services.value = [] // 确保有默认值
    throw error // 重新抛出错误，让调用者知道失败了
  }
}

const fetchStudents = async () => {
  try {
    const response = await listUser({
      pageNum: 1,
      pageSize: 100,
      deptId: 201, // 学生部门ID
    })
    students.value = response.rows || []
    console.log('学生列表加载成功:', students.value.length, '个学生')

    if (students.value.length > 0) {
      selectedStudentId.value =
        students.value[0].userName || students.value[0].num
    }
  } catch (error) {
    console.error('获取学生列表失败:', error)
    message.error('获取学生列表失败')
    students.value = [] // 确保有默认值
    throw error // 重新抛出错误，让调用者知道失败了
  }
}

// 获取所有服务的指标数据
const fetchAllIndicators = async () => {
  try {
    const indicatorsMap = {}

    // 为每个服务获取指标数据
    for (const service of services.value) {
      const response = await getIndicatorList(service.dictValue, {
        pageNum: 1,
        pageSize: 100, // 确保获取所有指标
      })
      indicatorsMap[service.dictValue] = response.rows || []
    }

    indicatorsData.value = indicatorsMap
    console.log(
      '指标数据加载完成:',
      Object.keys(indicatorsMap).length,
      '个服务',
    )
  } catch (error) {
    console.error('获取指标数据失败:', error)
    message.error('获取指标数据失败')
    indicatorsData.value = {}
    throw error
  }
}

const fetchAllMarksData = async () => {
  try {
    const response = await listMarks({
      pageNum: 1,
      pageSize: 5000, // 获取所有数据
    })
    marksData.value = response.rows || []
    console.log('评分数据加载成功:', marksData.value.length, '条记录')
  } catch (error) {
    console.error('获取评分数据失败:', error)
    message.error('获取评分数据失败')
    marksData.value = [] // 确保有默认值
    throw error // 重新抛出错误，让调用者知道失败了
  }
}

// 获取全班所有评分数据（不带学号筛选）
const fetchAllClassMarksData = async () => {
  try {
    const response = await listMarks({
      pageNum: 1,
      pageSize: 5000, // 获取所有数据，不带学号筛选
    })
    allClassMarksData.value = response.rows || []
    console.log('全班评分数据加载成功:', allClassMarksData.value.length, '条记录')
  } catch (error) {
    console.error('获取全班评分数据失败:', error)
    message.error('获取全班评分数据失败')
    allClassMarksData.value = []
    throw error
  }
}

const calculateStudentOverallScore = (studentMarks) => {
  if (studentMarks.length === 0) return 0

  const serviceScores = {}

  // 按服务分组计算分数
  services.value.forEach((service) => {
    const serviceMarks = studentMarks.filter(
      (mark) => mark.service === service.dictValue,
    )
    if (serviceMarks.length > 0) {
      serviceScores[service.dictValue] = calculateServiceScore(serviceMarks)
    }
  })

  const scores = Object.values(serviceScores)
  return scores.length > 0
    ? scores.reduce((sum, score) => sum + score, 0) / scores.length
    : 0
}

const calculateServiceScore = (serviceMarks) => {
  let totalScore = 0

  dimensions.forEach((dimension) => {
    const dimensionMarks = serviceMarks.filter(
      (mark) => mark.dimensionality === dimension,
    )

    let studentScore = 0,
      teacherScore = 0,
      enterpriseScore = 0

    dimensionMarks.forEach((mark) => {
      const score = parseFloat(mark.ratingCriteriaMark) || 0
      switch (mark.identity) {
        case '学生':
          studentScore = score
          break
        case '教师':
          teacherScore = score
          break
        case '企业':
          enterpriseScore = score
          break
      }
    })

    // 按权重计算维度得分：学生10% + 教师50% + 企业40%
    const dimensionScore =
      studentScore * 0.1 + teacherScore * 0.5 + enterpriseScore * 0.4
    // 每个维度占总分的20%
    totalScore += dimensionScore * 0.2
  })

  return totalScore
}

// 获取学生的完整答题数据
const fetchStudentMarksData = async (studentNum) => {
  if (!studentNum) return []

  let allMarks = []
  let pageNum = 1
  const pageSize = 200
  let hasMoreData = true

  try {
    while (hasMoreData) {
      const response = await listMarks({
        pageNum,
        pageSize,
        num: studentNum,
      })

      const marks = response.rows || []
      allMarks = allMarks.concat(marks)

      // 判断是否还有更多数据
      const total = response.total || 0
      hasMoreData = allMarks.length < total
      pageNum++

      console.log(
        `获取学生 ${studentNum} 第 ${pageNum - 1} 页数据: ${marks.length} 条，总计: ${allMarks.length}/${total}`,
      )
    }

    console.log(
      `学生 ${studentNum} 答题数据获取完成，共 ${allMarks.length} 条记录`,
    )
    return allMarks
  } catch (error) {
    console.error(`获取学生 ${studentNum} 答题数据失败:`, error)
    return []
  }
}

// 将评分等级转换为数值
const convertGradeToScore = (grade) => {
  const gradeMap = {
    A: 100,
    B: 80,
    C: 60,
    D: 40,
    E: 20,
  }
  return gradeMap[grade] || 0
}

const updateGrowthTrend = async () => {
  if (!selectedStudentId.value) {
    // 清空图表数据
    growthTrendOption.value.xAxis.data = []
    growthTrendOption.value.series = []
    return
  }

  // 获取选中学生的完整答题数据
  const studentMarks = await fetchStudentMarksData(selectedStudentId.value)

  // 无论是否有数据，都要显示完整的服务列表和维度结构
  const serviceNames = services.value.map((s) => s.dictLabel)

  if (studentMarks.length === 0) {
    console.warn(`学生 ${selectedStudentId.value} 无答题数据，显示空数据结构`)
    // 创建空数据系列，但保持结构完整
    const emptySeries = dimensions.map((dimension, index) => {
      const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
      return {
        name: dimension,
        type: 'line',
        data: new Array(services.value.length).fill(0), // 所有服务都显示0
        smooth: true,
        lineStyle: {
          color: colors[index],
          width: 3,
        },
        itemStyle: {
          color: colors[index],
        },
      }
    })

    growthTrendOption.value.xAxis.data = serviceNames
    growthTrendOption.value.series = emptySeries
    console.log('折线图空数据结构已设置:', { serviceNames, seriesCount: emptySeries.length })
    return
  }

  // 为每个维度创建一条线
  const series = dimensions.map((dimension, index) => {
    const data = services.value.map((service) => {
      // 获取该服务该维度的所有答题记录
      const serviceMarks = studentMarks.filter(
        (mark) =>
          mark.service === service.dictValue &&
          mark.dimensionality === dimension,
      )

      if (serviceMarks.length === 0) return 0

      // 获取该服务该维度的指标数量
      const serviceIndicators = indicatorsData.value[service.dictValue] || []
      const dimensionIndicators = serviceIndicators.filter(
        (indicator) => indicator.dimensionality === dimension,
      )
      const totalQuestions = dimensionIndicators.length

      if (totalQuestions === 0) {
        console.warn(
          `服务 ${service.dictLabel} 的维度 ${dimension} 没有找到指标数据`,
        )
        return 0
      }

      // 计算该维度的平均分
      let totalScore = 0
      let answeredQuestions = 0

      serviceMarks.forEach((mark) => {
        const score = convertGradeToScore(mark.ratingCriteriaMark)
        if (score > 0) {
          totalScore += score
          answeredQuestions++
        }
      })

      const averageScore =
        answeredQuestions > 0 ? totalScore / answeredQuestions : 0

      console.log(
        `学生 ${selectedStudentId.value} - 服务 ${service.dictLabel} - 维度 ${dimension}:`,
        {
          totalQuestions,
          answeredQuestions,
          averageScore: averageScore.toFixed(1),
        },
      )

      // 如果没有答题，返回0；如果有答题，计算平均分
      return averageScore
    })

    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']

    return {
      name: dimension,
      type: 'line',
      data: data,
      smooth: true,
      lineStyle: {
        width: 3,
        color: colors[index],
      },
      itemStyle: {
        color: colors[index],
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: colors[index] + '40',
            },
            {
              offset: 1,
              color: colors[index] + '10',
            },
          ],
        },
      },
    }
  })

  growthTrendOption.value.xAxis.data = serviceNames
  growthTrendOption.value.series = series
}

// 清空所有图表数据
const clearAllCharts = () => {
  console.log('清空所有图表数据')
  // 清空折线图
  growthTrendOption.value.xAxis.data = []
  growthTrendOption.value.series = []

  // 清空柱状图
  tasksBarOption.value.xAxis.data = []
  tasksBarOption.value.series = []

  // 清空雷达图
  classRadarOption.value.series = [{
    type: 'radar',
    data: [],
  }]
}

// 处理服务对比选择变化
const handleServiceComparisonChange = () => {
  console.log('服务对比选择变化:', {
    service1: selectedService1.value,
    service2: selectedService2.value
  })
  updateServiceComparison()
}

// 处理学生选择变化
const handleStudentChange = async () => {
  console.log('学生选择变化，重新获取数据:', selectedStudentId.value)

  // 先清空所有图表数据，避免显示上一个学生的数据
  clearAllCharts()

  // 重新获取所有数据
  await fetchAllMarksData()
  await updateGrowthTrend()
  updateClassRadar()
  await updateTasksBar()
}

const updateClassRadar = () => {
  // 计算全班每个维度的平均分
  const classAverages = dimensions.map((dimension) => {
    const dimensionScores = students.value
      .map((student) => {
        const studentMarks = marksData.value.filter(
          (mark) =>
            mark.num === student.userName && mark.dimensionality === dimension,
        )

        if (studentMarks.length === 0) return 0

        let totalScore = 0
        let serviceCount = 0

        services.value.forEach((service) => {
          const serviceMarks = studentMarks.filter(
            (mark) => mark.service === service.dictValue,
          )
          if (serviceMarks.length > 0) {
            let studentScore = 0,
              teacherScore = 0,
              enterpriseScore = 0

            serviceMarks.forEach((mark) => {
              const score = parseFloat(mark.ratingCriteriaMark) || 0
              switch (mark.identity) {
                case '学生':
                  studentScore = score
                  break
                case '教师':
                  teacherScore = score
                  break
                case '企业':
                  enterpriseScore = score
                  break
              }
            })

            totalScore +=
              studentScore * 0.1 + teacherScore * 0.5 + enterpriseScore * 0.4
            serviceCount++
          }
        })

        return serviceCount > 0 ? totalScore / serviceCount : 0
      })
      .filter((score) => score > 0)

    return dimensionScores.length > 0
      ? dimensionScores.reduce((sum, score) => sum + score, 0) /
          dimensionScores.length
      : 0
  })

  classRadarOption.value.series[0].data = [
    {
      value: classAverages,
      name: '全班平均',
      itemStyle: {
        color: '#00d4ff',
      },
      areaStyle: {
        color: 'rgba(0, 212, 255, 0.3)',
      },
    },
  ]
}

// 计算指定服务的全班五力平均分
const calculateServiceAverages = (serviceValue) => {
  if (!serviceValue || allClassMarksData.value.length === 0) {
    return new Array(dimensions.length).fill(0)
  }

  return dimensions.map((dimension) => {
    // 获取该服务该维度的所有评分记录
    const serviceMarks = allClassMarksData.value.filter(
      (mark) =>
        mark.service === serviceValue && mark.dimensionality === dimension,
    )

    if (serviceMarks.length === 0) return 0

    // 按学生分组计算加权平均分
    const studentGroups = {}
    serviceMarks.forEach((mark) => {
      if (!studentGroups[mark.num]) {
        studentGroups[mark.num] = { student: 0, teacher: 0, enterprise: 0 }
      }

      const score = convertGradeToScore(mark.ratingCriteriaMark)
      switch (mark.identity) {
        case '学生':
          studentGroups[mark.num].student = score
          break
        case '教师':
          studentGroups[mark.num].teacher = score
          break
        case '企业':
          studentGroups[mark.num].enterprise = score
          break
      }
    })

    // 计算每个学生的维度得分，然后求平均
    const studentScores = Object.values(studentGroups).map((group) => {
      return group.student * 0.1 + group.teacher * 0.5 + group.enterprise * 0.4
    }).filter(score => score > 0)

    return studentScores.length > 0
      ? studentScores.reduce((sum, score) => sum + score, 0) / studentScores.length
      : 0
  })
}

// 更新服务对比雷达图
const updateServiceComparison = () => {
  if (!selectedService1.value && !selectedService2.value) {
    // 如果没有选择服务，显示空雷达图
    classRadarOption.value.series[0].data = []
    return
  }

  const seriesData = []

  // 添加服务1的数据
  if (selectedService1.value) {
    const service1Averages = calculateServiceAverages(selectedService1.value)
    const service1Name = services.value.find(s => s.dictValue === selectedService1.value)?.dictLabel || '服务1'

    seriesData.push({
      value: service1Averages,
      name: service1Name,
      itemStyle: {
        color: '#ff6b6b',
      },
      areaStyle: {
        color: 'rgba(255, 107, 107, 0.2)',
      },
    })
  }

  // 添加服务2的数据
  if (selectedService2.value) {
    const service2Averages = calculateServiceAverages(selectedService2.value)
    const service2Name = services.value.find(s => s.dictValue === selectedService2.value)?.dictLabel || '服务2'

    seriesData.push({
      value: service2Averages,
      name: service2Name,
      itemStyle: {
        color: '#4ecdc4',
      },
      areaStyle: {
        color: 'rgba(78, 205, 196, 0.2)',
      },
    })
  }

  classRadarOption.value.series[0].data = seriesData

  console.log('雷达图服务对比数据更新完成:', {
    service1: selectedService1.value,
    service2: selectedService2.value,
    seriesCount: seriesData.length
  })
}

// 更新八任务柱状图 - 复用学生成长趋势的数据
const updateTasksBar = async () => {
  if (!selectedStudentId.value) {
    // 清空图表数据
    tasksBarOption.value.xAxis.data = []
    tasksBarOption.value.series = []
    return
  }

  // 获取选中学生的完整答题数据（复用成长趋势的逻辑）
  const studentMarks = await fetchStudentMarksData(selectedStudentId.value)

  // 无论是否有数据，都要显示完整的服务列表和维度结构
  const serviceNames = services.value.map((s) => s.dictLabel)
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']

  if (studentMarks.length === 0) {
    console.warn(`学生 ${selectedStudentId.value} 无答题数据，显示空数据结构`)
    // 创建空数据系列，但保持结构完整
    const emptySeries = dimensions.map((dimension, index) => ({
      name: dimension,
      type: 'bar',
      data: new Array(services.value.length).fill(0), // 所有服务都显示0
      itemStyle: {
        color: colors[index]
      },
      barWidth: '12%',
      barGap: '10%',
    }))

    tasksBarOption.value.xAxis.data = serviceNames
    tasksBarOption.value.series = emptySeries
    tasksBarOption.value.legend.data = dimensions
    console.log('柱状图空数据结构已设置:', { serviceNames, seriesCount: emptySeries.length })

    // 强制刷新图表
    await nextTick()
    if (tasksBarChart.value) {
      tasksBarChart.value.resize()
    }
    return
  }

  // 为每个维度创建一个系列（柱子）
  const series = dimensions.map((dimension, index) => {
    const data = services.value.map((service) => {
      // 获取该服务该维度的所有答题记录
      const serviceMarks = studentMarks.filter(
        (mark) =>
          mark.service === service.dictValue &&
          mark.dimensionality === dimension,
      )

      if (serviceMarks.length === 0) {
        console.log(`服务 ${service.dictLabel} 维度 ${dimension}: 无答题记录`)
        return 0
      }

      // 获取该服务该维度的指标数量
      const serviceIndicators = indicatorsData.value[service.dictValue] || []
      const dimensionIndicators = serviceIndicators.filter(
        (indicator) => indicator.dimensionality === dimension,
      )
      const totalQuestions = dimensionIndicators.length

      if (totalQuestions === 0) {
        console.log(`服务 ${service.dictLabel} 维度 ${dimension}: 无指标数据`)
        return 0
      }

      // 计算该维度的平均分
      let totalScore = 0
      let answeredQuestions = 0

      serviceMarks.forEach((mark) => {
        const score = convertGradeToScore(mark.ratingCriteriaMark)
        if (score > 0) {
          totalScore += score
          answeredQuestions++
        }
      })

      const averageScore =
        answeredQuestions > 0 ? totalScore / answeredQuestions : 0
      console.log(
        `柱状图 - 服务 ${service.dictLabel} 维度 ${dimension}: ${averageScore.toFixed(1)}`,
      )
      return averageScore
    })

    return {
      name: dimension,
      type: 'bar',
      data: data,
      itemStyle: {
        color: colors[index],
      },
      barWidth: '12%', // 控制柱子宽度，5个柱子需要更窄一些
      barGap: '10%', // 柱子间距
    }
  })

  // 更新图表配置
  tasksBarOption.value.xAxis.data = serviceNames
  tasksBarOption.value.series = series
  tasksBarOption.value.legend.data = dimensions // 确保图例数据正确

  // 强制刷新图表
  await nextTick()
  if (tasksBarChart.value) {
    tasksBarChart.value.resize()
  }

  console.log('八任务柱状图数据更新完成:', {
    services: serviceNames.length,
    dimensions: dimensions.length,
    seriesCount: series.length,
    serviceNames,
    sampleData: series.length > 0 ? series[0].data : [],
  })
}

const refreshData = async () => {
  loading.value = true
  try {
    await fetchAllMarksData()

    // 确保数据加载完成后再更新图表
    await nextTick()

    await updateGrowthTrend()
    updateClassRadar()
    await updateTasksBar()

    // 数据更新后重新计算缩放
    await nextTick()
    updateScale()

    // 延迟一下再触发图表resize，确保DOM已经渲染完成
    setTimeout(() => {
      if (growthTrendChart.value) {
        growthTrendChart.value.resize()
      }
      if (classRadarChart.value) {
        classRadarChart.value.resize()
      }
      if (tasksBarChart.value) {
        tasksBarChart.value.resize()
      }
    }, 100)

    console.log('数据刷新完成')
  } catch (error) {
    console.error('刷新数据失败:', error)
    message.error('数据刷新失败，请重试')
  } finally {
    loading.value = false
  }
}

const startAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }

  if (autoRefresh.value) {
    refreshTimer.value = setInterval(() => {
      refreshData()
    }, refreshInterval.value * 1000)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

const toggleAutoRefresh = (checked) => {
  if (checked) {
    startAutoRefresh()
    message.success(`已开启自动刷新，每${refreshInterval.value}秒更新一次`)
  } else {
    stopAutoRefresh()
    message.info('已关闭自动刷新')
  }
}

const goBack = () => {
  router.push('/')
}

// 监听选中学生变化
watch(selectedStudentId, async (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    console.log('监听到学生变化:', oldValue, '->', newValue)

    // 先清空所有图表数据，避免显示上一个学生的数据
    clearAllCharts()

    // 重新获取所有数据
    await fetchAllMarksData()
    await updateGrowthTrend()
    updateClassRadar()
    await updateTasksBar()
  }
})

// 组件挂载
onMounted(async () => {
  loading.value = true
  try {
    // 确保用户已登录且有权限
    if (!authStore.isAuthenticated) {
      console.warn('用户未登录，跳转到首页')
      window.location.href = '/'
      return
    }

    // 如果用户信息不存在，先获取用户信息
    if (!authStore.userInfo) {
      try {
        await authStore.getProfile()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        window.location.href = '/'
        return
      }
    }

    // 检查用户权限
    const userDeptId = authStore.userInfo?.deptId
    const allowedRoles = [103, 202, 203] // 管理员、教师和企业
    if (!allowedRoles.includes(userDeptId)) {
      console.warn('用户权限不足，无法访问此页面')
      window.location.href = '/'
      return
    }

    // 按顺序加载数据，避免并发问题
    await fetchServices()
    await fetchAllIndicators() // 获取指标数据
    await fetchAllClassMarksData() // 获取全班数据用于服务对比
    await fetchStudents()
    await refreshData()

    // 设置默认的服务对比选择
    if (services.value.length >= 2) {
      selectedService1.value = services.value[0].dictValue
      selectedService2.value = services.value[1].dictValue
      updateServiceComparison()
    }

    // 启动自动刷新
    if (autoRefresh.value) {
      startAutoRefresh()
    }

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)

    // 初始化缩放
    await nextTick()
    updateScale()
  } catch (error) {
    console.error('页面初始化失败:', error)
    message.error('页面加载失败，请刷新重试')
  } finally {
    loading.value = false
  }
})

// 组件卸载
onUnmounted(() => {
  stopAutoRefresh()
  window.removeEventListener('resize', handleResize)
  if (resizeTimer.value) {
    clearTimeout(resizeTimer.value)
  }
})
</script>

<style scoped>
/* 全屏背景层 */
.dashboard-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1e3f 0%, #1a365d 50%, #2d5a87 100%);
  z-index: 999;
}

.dashboard-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(
      circle at 20% 80%,
      rgba(0, 212, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(0, 212, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(0, 212, 255, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.dashboard-container {
  width: 1920px;
  height: 1080px;
  background: transparent;
  color: #ffffff;
  padding: 16px;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  transform-origin: top left;
  transition: transform 0.3s ease;
  box-sizing: border-box; /* 确保padding包含在尺寸内 */
}

.dashboard-container > * {
  position: relative;
  z-index: 1;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 16px;
  height: 80px;
  flex-shrink: 0;
  min-height: 80px; /* 确保最小高度 */
}

.dashboard-header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  border: 1px solid rgba(0, 212, 255, 0.5) !important;
  color: #00d4ff !important;
  background: rgba(0, 212, 255, 0.1) !important;
  transition: all 0.3s ease;
}

.back-button:hover {
  border-color: #00d4ff !important;
  background: rgba(0, 212, 255, 0.2) !important;
  color: #ffffff !important;
  transform: translateX(-5px);
}

.dashboard-title {
  font-size: 32px;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(45deg, #00d4ff, #0099cc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.dashboard-content {
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  padding-bottom: 20px; /* 添加底部安全边距 */
  min-height: 0; /* 允许容器收缩 */
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  color: #00d4ff;
  margin-bottom: 8px;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.stat-label {
  font-size: 16px;
  color: #b3d9ff;
  font-weight: 500;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  flex: 1;
  overflow: visible;
}

.chart-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  overflow: hidden; /* 防止内容溢出 */
  display: flex;
  flex-direction: column;
  min-height: 400px; /* 设置最小高度 */
}

.chart-section.large {
  grid-row: span 2;
  min-height: 600px; /* 大图表更高 */
}

.chart-title {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 16px 0;
  text-align: center;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  flex-shrink: 0; /* 防止标题被压缩 */
}

.student-growth-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 500px; /* 设置明确的最小高度 */
}

.growth-chart-wrapper,
.class-radar-wrapper,
.tasks-radar-wrapper {
  flex: 1;
  min-height: 300px; /* 设置明确的最小高度 */
  height: 300px; /* 设置固定高度 */
  overflow: hidden;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 服务对比选择器样式 */
.service-comparison-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  justify-content: center;
  align-items: center;
}

.service-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-selector label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  min-width: 60px;
}

/* 自定义选择器和日期选择器样式 */
:deep(.ant-select-selector) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  color: #ffffff !important;
}

:deep(.ant-select-selection-item) {
  color: #ffffff !important;
}

:deep(.ant-select-arrow) {
  color: #00d4ff !important;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-title {
    font-size: 28px;
  }

  .stats-overview {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  .stat-number {
    font-size: 28px;
  }
}

@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
    grid-template-rows: 500px 400px 400px; /* 设置明确的行高 */
    gap: 24px;
    min-height: 1348px; /* 500 + 400 + 400 + 24*2(gaps) */
  }

  .chart-section.large {
    grid-row: span 1;
    min-height: 500px;
  }

  .chart-section {
    min-height: 400px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .stats-overview {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .dashboard-title {
    font-size: 24px;
  }

  .dashboard-controls {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
