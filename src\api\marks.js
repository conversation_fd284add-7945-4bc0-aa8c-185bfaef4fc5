import request from '@/utils/request'

/**
 * 成绩信息接口
 * @typedef {Object} Marks
 * @property {string|null} [createBy] - 创建人
 * @property {string|null} [createTime] - 创建时间
 * @property {string|null} [updateBy] - 更新人
 * @property {string|null} [updateTime] - 更新时间
 * @property {string|null} [remark] - 备注
 * @property {number|null} id - 成绩ID
 * @property {string} num - 学号
 * @property {string} name - 姓名
 * @property {string} dimensionality - 维度
 * @property {string} service - 服务项目
 * @property {string} evaluatingIndicator - 评价指标
 * @property {string} identity - 身份
 * @property {string} ratingCriteriaMark - 评分等级 (A/B/C/D/E)
 */

/**
 * 查询成绩列表
 * @param {Object} [params] - 查询参数
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 页面大小
 * @param {string} [params.num] - 学号
 * @param {string} [params.service] - 服务名称
 * @returns {Promise<import('@/types').ApiResponse<Marks[]>>} 成绩列表
 */
export function listMarks(params) {
  return request({
    url: '/hk/marks/list',
    method: 'get',
    params,
  })
}

/**
 * 查询成绩详情
 * @param {number} id - 成绩ID
 * @returns {Promise<import('@/types').ApiResponse<Marks>>} 成绩详情
 */
export function getMarks(id) {
  return request({
    url: `/hk/marks/${id}`,
    method: 'get',
  })
}

/**
 * 新增成绩
 * @param {Omit<Marks, 'id'>} data - 成绩数据 (不包含ID)
 * @returns {Promise<import('@/types').ApiResponse<Marks>>} 新增结果
 */
export function addMarks(data) {
  return request({
    url: '/hk/marks',
    method: 'post',
    data,
  })
}

/**
 * 修改成绩
 * @param {Marks} data - 成绩数据
 * @returns {Promise<import('@/types').ApiResponse<Marks>>} 修改结果
 */
export function updateMarks(data) {
  return request({
    url: '/hk/marks',
    method: 'put',
    data,
  })
}

/**
 * 删除成绩
 * @param {number} id - 成绩ID
 * @returns {Promise<import('@/types').ApiResponse<void>>} 删除结果
 */
export function delMarks(id) {
  return request({
    url: `/hk/marks/${id}`,
    method: 'delete',
  })
}
