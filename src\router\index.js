import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

/**
 * @typedef {import('vue-router').RouteRecordRaw} RouteRecordRaw
 */

/** @type {RouteRecordRaw[]} */
const routes = [
  {
    path: '/',
    name: 'RoleSelection',
    component: () => import('@/views/RoleSelectionView.vue'),
    meta: {
      title: '身份选择',
    },
  },
  {
    path: '/student-selection',
    name: 'StudentSelection',
    component: () => import('@/views/StudentSelectionView.vue'),
    meta: {
      title: '学生选择',
    },
  },
  {
    path: '/evaluation/:studentId',
    name: 'Evaluation',
    component: () => import('@/views/EvaluationView.vue'),
    meta: {
      title: '评价打分',
    },
  },
  {
    path: '/score/:studentId',
    name: 'Score',
    component: () => import('@/views/ScoreView.vue'),
    meta: {
      title: '成绩展示',
    },
  },
  {
    path: '/dashboard/single-task/:service',
    name: 'SingleTaskDashboard',
    component: () => import('@/views/dashboard/SingleTaskDashboardView.vue'),
    meta: {
      title: '单任务分析大屏',
      requiresAuth: true,
      allowedRoles: [103, 202, 203], // 管理员、教师和企业
    },
  },
  {
    path: '/dashboard/overall-analysis',
    name: 'OverallAnalysisDashboard',
    component: () => import('@/views/dashboard/OverallAnalysisDashboardView.vue'),
    meta: {
      title: '总评分析大屏',
      requiresAuth: true,
      allowedRoles: [103, 202, 203], // 管理员、教师和企业
    },
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 客舱设施与服务五力评价系统`
  } else {
    document.title = '客舱设施与服务五力评价系统'
  }

  // 权限检查
  if (to.meta?.requiresAuth) {
    const authStore = useAuthStore()

    // 检查是否已登录
    if (!authStore.isAuthenticated) {
      console.warn('访问受保护页面需要登录')
      // 保存目标路由，登录后跳转
      sessionStorage.setItem('redirectAfterLogin', to.fullPath)
      next('/')
      return
    }

    // 如果用户信息不存在，尝试获取
    if (!authStore.userInfo) {
      try {
        await authStore.getProfile()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 保存目标路由，登录后跳转
        sessionStorage.setItem('redirectAfterLogin', to.fullPath)
        next('/')
        return
      }
    }

    // 检查角色权限
    if (to.meta?.allowedRoles) {
      const userDeptId = authStore.userInfo?.deptId
      if (!userDeptId || !to.meta.allowedRoles.includes(userDeptId)) {
        console.warn('用户权限不足，无法访问此页面')
        next('/')
        return
      }
    }
  }

  // 检查是否有保存的重定向路径
  if (to.path === '/' && sessionStorage.getItem('redirectAfterLogin')) {
    const redirectPath = sessionStorage.getItem('redirectAfterLogin')
    sessionStorage.removeItem('redirectAfterLogin')
    next(redirectPath)
    return
  }

  next()
})

export default router
