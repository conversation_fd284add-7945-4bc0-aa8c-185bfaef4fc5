<template>
  <div class="max-w-4xl mx-auto">
    <!-- 步骤指示器 -->
    <div class="mb-8">
      <a-steps :current="authStore.isAuthenticated ? evaluationStore.currentStep - 1 : -1" size="small">
        <a-step
          title="选择服务"
          description="选择要评价的服务项目"
          :class="{ 'clickable-step': authStore.isAuthenticated && evaluationStore.currentStep > 1 }"
          @click="handleStepClick(1)"
        />
        <a-step title="选择身份" description="选择您的评价身份" />
        <a-step title="开始评价" description="选择学生进行评价" />
      </a-steps>
    </div>

    <!-- 未登录提示 -->
    <div v-if="!authStore.isAuthenticated" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
      <div class="flex items-center justify-center">
        <i class="fa fa-exclamation-triangle text-yellow-600 mr-2"></i>
        <span class="text-yellow-700">请先登录后再进行评价操作</span>
      </div>
    </div>

    <!-- 第一步：选择服务 -->
    <div v-if="!authStore.isAuthenticated || evaluationStore.currentStep === 1" class="text-center">
      <h2 class="text-3xl font-bold text-primary mb-8">请选择要评价的服务</h2>

      <div class="mb-6">
        <a-select
          v-model:value="selectedServiceValue"
          placeholder="请选择服务项目"
          style="width: 400px"
          size="large"
          :disabled="!authStore.isAuthenticated"
          @change="handleServiceChange"
        >
          <a-select-option
            v-for="service in evaluationStore.services"
            :key="service.dictCode"
            :value="service.dictValue"
          >
            {{ service.dictLabel }}
          </a-select-option>
        </a-select>
      </div>

      <div class="mt-8">
        <a-button
          type="primary"
          size="large"
          :disabled="!selectedServiceValue || !authStore.isAuthenticated"
          @click="confirmServiceSelection"
        >
          下一步：选择身份
        </a-button>
      </div>
    </div>

    <!-- 第二步：选择角色 -->
    <div v-else-if="authStore.isAuthenticated && evaluationStore.currentStep === 2" class="text-center">
      <h2 class="text-3xl font-bold text-primary mb-4">请选择您的评价身份</h2>

      <!-- 显示已选择的服务 -->
      <div class="mb-8 p-4 bg-blue-50 rounded-lg">
        <p class="text-blue-700 !m-0">
          <i class="fa fa-check-circle mr-2"></i>
          已选择服务：<strong>{{ evaluationStore.selectedService?.dictLabel }}</strong>
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <button
          v-for="role in roleWeights"
          :key="role.role"
          @click="selectRole(role.role)"
          :disabled="!authStore.isAuthenticated || !isRoleAllowed(role.role)"
          :class="[
            'bg-white rounded-xl shadow p-6 transition-all duration-200',
            !authStore.isAuthenticated || !isRoleAllowed(role.role)
              ? 'opacity-50 cursor-not-allowed'
              : 'card-hover'
          ]"
        >
          <div class="text-4xl mb-3 text-accent">
            <i :class="`fa ${role.icon}`"></i>
          </div>
          <h3 class="text-xl font-semibold mb-1">{{ role.name }}</h3>
          <p class="text-gray-600">在总分中的占比：{{ role.weight }}%</p>
          <!-- 权限提示 -->
          <div v-if="authStore.isAuthenticated && !isRoleAllowed(role.role)" class="mt-2">
            <a-tag color="orange" size="small">
              <i class="fa fa-lock mr-1"></i>
              权限不足
            </a-tag>
          </div>
        </button>
      </div>

      <div class="flex justify-center space-x-4 gap-2">
        <a-button @click="goBackToServiceSelection">
          <i class="fa fa-arrow-left mr-1"></i> 重新选择服务
        </a-button>
        <a-button
          type="primary"
          @click="viewScores"
          :disabled="!authStore.isAuthenticated"
        >
          <i class="fa fa-bar-chart mr-1"></i> 查看成绩
        </a-button>
        <!-- 大屏入口 - 仅管理员、教师和企业用户可见 -->
        <a-dropdown v-if="isTeacherOrEnterprise" placement="topCenter">
          <a-button type="primary" style="background: linear-gradient(45deg, #667eea 0%, #764ba2 100%); border: none;">
            <i class="fa fa-desktop mr-1"></i> 数据大屏
            <i class="fa fa-angle-down ml-1"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="single-task" @click="goToSingleTaskDashboard">
                <i class="fa fa-chart-radar mr-2"></i>
                单任务分析大屏
              </a-menu-item>
              <a-menu-item key="overall-analysis" @click="goToOverallAnalysisDashboard">
                <i class="fa fa-chart-line mr-2"></i>
                总评分析大屏
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <!-- 第三步提示（实际会跳转到其他页面） -->
    <div v-else-if="authStore.isAuthenticated && evaluationStore.currentStep === 3" class="text-center">
      <h2 class="text-3xl font-bold text-primary mb-8">正在跳转到评价页面...</h2>
      <a-spin size="large" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useEvaluationStore } from '@/stores/evaluation'
import { useAuthStore } from '@/stores/auth'
import { roleWeights } from '@/constants/data'
import { message } from 'ant-design-vue'
import {
  Steps as ASteps,
  Step as AStep,
  Select as ASelect,
  SelectOption as ASelectOption,
  Button as AButton,
  Spin as ASpin,
  Tag as ATag,
  Dropdown as ADropdown,
  Menu as AMenu,
  MenuItem as AMenuItem,
} from 'ant-design-vue'

/**
 * @typedef {import('@/types').Role} Role
 */

const router = useRouter()
const evaluationStore = useEvaluationStore()
const authStore = useAuthStore()

const selectedServiceValue = ref('')

// 初始化服务列表的函数
const initializeServices = async () => {
  if (authStore.isAuthenticated) {
    // 从localStorage恢复状态
    await evaluationStore.initializeFromStorage()

    // 获取服务列表和维度列表
    await Promise.all([
      evaluationStore.fetchServices(),
      evaluationStore.fetchDimensionalities()
    ])

    // 如果已经选择了服务，设置下拉框的值
    if (evaluationStore.selectedService) {
      selectedServiceValue.value = evaluationStore.selectedService.dictValue
    }
  } else {
    // 未登录时重置状态
    evaluationStore.resetSelection()
    selectedServiceValue.value = ''
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await initializeServices()
})

// 监听登录状态变化
watch(
  () => authStore.isAuthenticated,
  async (isAuthenticated) => {
    if (isAuthenticated) {
      // 登录后重新初始化服务列表
      await initializeServices()
    } else {
      // 退出登录时重置状态
      evaluationStore.resetSelection()
      selectedServiceValue.value = ''
    }
  }
)

// 处理服务选择变化
const handleServiceChange = (value) => {
  selectedServiceValue.value = value
}

// 确认服务选择
const confirmServiceSelection = async () => {
  if (!selectedServiceValue.value) {
    message.warning('请选择一个服务项目')
    return
  }

  const selectedService = evaluationStore.services.find(
    service => service.dictValue === selectedServiceValue.value
  )

  if (selectedService) {
    await evaluationStore.selectService(selectedService)
    message.success(`已选择服务：${selectedService.dictLabel}`)
  }
}

// 返回服务选择
const goBackToServiceSelection = () => {
  evaluationStore.setCurrentStep(1)
  evaluationStore.selectedService = null
  localStorage.removeItem('selectedService')
}

/**
 * 处理步骤点击
 * @param {number} step - 步骤号
 */
const handleStepClick = (step) => {
  if (!authStore.isAuthenticated) {
    return
  }

  // 只允许点击已完成的步骤或当前步骤
  if (step === 1 && evaluationStore.currentStep > 1) {
    // 点击第一步：选择服务
    goBackToServiceSelection()
  }
  // 可以在这里添加其他步骤的点击处理逻辑
}

// 检查用户是否有权限选择某个角色
const isRoleAllowed = (role) => {
  if (!authStore.isAuthenticated || !authStore.userInfo) {
    return false
  }

  const userDeptId = authStore.userInfo.deptId

  // 根据用户部门ID限制可选择的角色
  switch (userDeptId) {
    case 201: // 学生
      return role === 'student'
    case 202: // 教师
      return role === 'teacher'
    case 203: // 企业
      return role === 'enterprise'
    case 101: // 管理员
      return true // 管理员可以选择所有角色
    default:
      // 其他用户可以选择所有角色（因为权限不对会接口都不让访问）
      return true
  }
}

/**
 * 选择角色
 * @param {Role} role - 角色类型
 */
const selectRole = (role) => {
  if (!authStore.isAuthenticated) {
    message.warning('请先登录')
    return
  }

  if (!evaluationStore.selectedService) {
    message.warning('请先选择服务项目')
    return
  }

  // 检查权限
  if (!isRoleAllowed(role)) {
    message.warning('您没有权限选择此角色')
    return
  }

  evaluationStore.selectRole(role)
  evaluationStore.setViewingScores(false)

  // 所有用户选择角色后都进入学生选择页面
  // 用户可以选择评价任何学生，包括自己
  router.push('/student-selection')
}

const viewScores = () => {
  if (!authStore.isAuthenticated) {
    message.warning('请先登录')
    return
  }

  if (!evaluationStore.selectedService) {
    message.warning('请先选择服务项目')
    return
  }

  // 所有用户都可以查看成绩，进入学生选择页面
  evaluationStore.setViewingScores(true)
  router.push('/student-selection')
}

// 计算属性：判断是否为教师、企业或管理员用户
const isTeacherOrEnterprise = computed(() => {
  if (!authStore.isAuthenticated || !authStore.userInfo) {
    return false
  }
  const userDeptId = authStore.userInfo.deptId
  return userDeptId === 103 || userDeptId === 202 || userDeptId === 203 // 管理员、教师或企业
})

// 跳转到单任务分析大屏
const goToSingleTaskDashboard = () => {
  if (!evaluationStore.selectedService) {
    message.warning('请先选择服务项目')
    return
  }

  const serviceValue = evaluationStore.selectedService.dictValue
  router.push(`/dashboard/single-task/${serviceValue}`)
}

// 跳转到总评分析大屏
const goToOverallAnalysisDashboard = () => {
  router.push('/dashboard/overall-analysis')
}
</script>

<style lang="scss" scoped>
.card-hover {
  transition: all 0.3s;
}

.card-hover:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-0.25rem);
}

.btn-primary {
  background-color: #0f3460;
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.btn-primary:hover {
  background-color: #1a508b;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.clickable-step {
  cursor: pointer !important;
  transition: all 0.2s ease;
}

.clickable-step:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}
</style> 