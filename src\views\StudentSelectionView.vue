<template>
  <div class="max-w-5xl mx-auto">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold text-primary">
        {{ isViewingScores ? '查看学生成绩' : '选择学生' }}
      </h2>
      <div class="flex space-x-3">
      </div>
    </div>
    
    <!-- 学生列表 -->
    <div class="bg-white rounded-lg shadow p-4">
      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead>
            <tr class="bg-gray-100 text-left">
              <th class="py-3 px-4 font-semibold text-gray-700">学号</th>
              <th class="py-3 px-4 font-semibold text-gray-700">姓名</th>
              <th class="py-3 px-4 font-semibold text-gray-700">评价进度</th>
              <th class="py-3 px-4 font-semibold text-gray-700">状态</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="student in students"
              :key="student.id"
              :class="[
                'border-b border-gray-200 hover:bg-gray-50 transition-colors cursor-pointer',
                student.isSelf ? 'bg-blue-50 border-blue-200' : ''
              ]"
              @click="handleStudentClick(student)"
            >
              <td class="py-3 px-4 font-medium text-gray-900">{{ student.id }}</td>
              <td class="py-3 px-4">
                <div class="flex items-center">
                  <span>{{ student.name }}</span>
                  <span v-if="student.isSelf" class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                    我自己
                  </span>
                </div>
              </td>
              <td class="py-3 px-4">
                <span class="text-sm text-gray-600">
                  {{ getStudentProgress(student.id) }}
                </span>
              </td>
              <td class="py-3 px-4">
                <span :class="getStatusClass(student.id)">
                  {{ getStudentStatus(student.id) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- 分页 -->
    <div class="mt-4 flex justify-end items-center">
      <span class="text-sm text-gray-700 mr-4">
        共 {{ total }} 条记录，第 {{ queryParams.pageNum }} / {{ totalPages }} 页
      </span>
      <div class="inline-flex rounded-md shadow-sm">
        <button
          @click="handlePageChange(queryParams.pageNum - 1)"
          :disabled="queryParams.pageNum <= 1"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          上一页
        </button>
        <button
          @click="handlePageChange(queryParams.pageNum + 1)"
          :disabled="queryParams.pageNum >= totalPages"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="mt-6 flex justify-between">
      <button @click="goBack" class="btn-secondary">
        <i class="fa fa-arrow-left mr-1"></i> 返回
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useEvaluationStore } from '@/stores/evaluation'
import { storeToRefs } from 'pinia'

/**
 * @typedef {import('@/types').Student} Student
 */

const router = useRouter()
const evaluationStore = useEvaluationStore()
const {
  students,
  selectedRole,
  isViewingScores,
  total,
  queryParams,
} = storeToRefs(evaluationStore)

const totalPages = computed(() => Math.ceil(total.value / queryParams.value.pageSize))

/**
 * 获取学生状态
 * @param {string} studentId - 学生ID
 * @returns {string} 学生状态
 */
const getStudentStatus = (studentId) => {
  const status = evaluationStore.getStudentEvaluationStatus(studentId)
  return status.status
}

/**
 * 获取学生评价进度
 * @param {string} studentId - 学生ID
 * @returns {string} 评价进度
 */
const getStudentProgress = (studentId) => {
  const status = evaluationStore.getStudentEvaluationStatus(studentId)
  return `${status.completed}/${status.total}`
}

/**
 * 获取状态样式类
 * @param {string} studentId - 学生ID
 * @returns {string} 样式类名
 */
const getStatusClass = (studentId) => {
  const status = getStudentStatus(studentId)
  switch (status) {
    case '已完成':
      return 'text-green-500'
    case '进行中':
      return 'text-yellow-500'
    default:
      return 'text-gray-500'
  }
}

/**
 * 处理学生点击事件
 * @param {Student} student - 学生对象
 */
const handleStudentClick = (student) => {
  if (isViewingScores.value) {
    // 查看成绩模式下，点击直接查看该学生的成绩
    router.push(`/score/${student.id}`)
  } else {
    // 评价模式下，点击直接进入评价
    router.push(`/evaluation/${student.id}`)
  }
}

const goBack = () => {
  // 只重置角色和学生选择，保留服务选择
  evaluationStore.resetRoleAndStudentSelection()
  router.push('/')
}

const handlePageChange = async (newPage) => {
  if (newPage > 0 && newPage <= totalPages.value) {
    evaluationStore.setQueryParams({ pageNum: newPage })
    // 分页变化后重新获取评价状态
    await evaluationStore.fetchAllStudentsEvaluationStatus()
  }
}

onMounted(async () => {
  await evaluationStore.fetchStudents()
  // 获取学生列表后，获取所有学生的评价状态
  await evaluationStore.fetchAllStudentsEvaluationStatus()
})

// 监听学生列表变化，重新获取评价状态
watch(
  () => students.value,
  async () => {
    if (students.value.length > 0) {
      await evaluationStore.fetchAllStudentsEvaluationStatus()
    }
  },
  { deep: true }
)

// 监听角色变化，重新获取评价状态
watch(
  () => selectedRole.value,
  async () => {
    if (selectedRole.value && students.value.length > 0) {
      await evaluationStore.fetchAllStudentsEvaluationStatus()
    }
  }
)
</script>

<style lang="scss" scoped>
.btn-primary {
  background-color: #0f3460;
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.btn-primary:hover {
  background-color: #1a508b;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #eef2f5;
  color: #0f3460;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style> 