<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <iframe
      id="inner-iframe"
      srcdoc='<!DOCTYPE html>
    <html>
      <head>
        <script>window._doubao_store = {"sourceCodeType":"html","imgPlaceholder":{"loading":"//lf-flow-web-cdn.doubao.com/obj/flow-doubao/doubao/web/static/image/html-image-loading.870bba3c.gif","reject":"//lf-flow-web-cdn.doubao.com/obj/flow-doubao/doubao/web/static/image/html-image-reject.0cc445a6.jpeg"}}</script>
      <title>Sandbox Document</title><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><script>
        ;(function (w, d, u, b, n, pc, ga, ae, po, s, p, e, t, pp) {pc = &apos;precollect&apos;;ga
        = &apos;getAttribute&apos;;ae = &apos;addEventListener&apos;;po = &apos;PerformanceObserver&apos;;s = function
        (m) {p = [].slice.call(arguments);p.push(Date.now(), location.href);(m == pc ?
        s.p.a : s.q).push(p)};s.q = [];s.p = { a: [] };w[n] = s;e =
        document.createElement(&apos;script&apos;);e.src = u + &apos;?bid=&apos; + b + &apos;&amp;globalName=&apos; +
        n;e.crossOrigin = u.indexOf(&apos;sdk-web&apos;) > 0 ? &apos;anonymous&apos; :
        &apos;use-credentials&apos;;d.getElementsByTagName(&apos;head&apos;)[0].appendChild(e);if (ae in w)
        {s.pcErr = function (e) {e = e || w.event;t = e.target || e.srcElement;if (t
        instanceof Element || t instanceof HTMLElement) {if (t[ga](&apos;integrity&apos;))
        {w[n](pc, &apos;sri&apos;, t[ga](&apos;href&apos;) || t[ga](&apos;src&apos;))} else {w[n](pc, &apos;st&apos;, { tagName:
        t.tagName, url: t[ga](&apos;href&apos;) || t[ga](&apos;src&apos;) })}} else {w[n](pc, &apos;err&apos;, e.error)}};s.pcRej = function (e) {e = e || w.event;w[n](pc, &apos;reject&apos;,
        e.reason || (e.detail &amp;&amp; e.detail.reason))};w[ae](&apos;error&apos;, s.pcErr,
        true);w[ae](&apos;unhandledrejection&apos;, s.pcRej,
        true);};if(&apos;PerformanceLongTaskTiming&apos; in w) {pp = s.pp = { entries: []
        };pp.observer = new PerformanceObserver(function (l) {pp.entries =
        pp.entries.concat(l.getEntries())});pp.observer.observe({ entryTypes:
        [&apos;longtask&apos;]
        })}})(window,document,&apos;https://lf3-short.ibytedapm.com/slardar/fe/sdk-web/browser.cn.js&apos;,&apos;flow_web&apos;,&apos;_slardar&apos;)
        window._slardar(&apos;init&apos;, {
            bid: &apos;flow_web&apos;,
            env: &apos;cn-release-production-samantha&apos;,
            release: &apos;*********&apos;,
            pid: &apos;html_preview_sandbox&apos;,
            plugins: {
              blankScreen: {
                screenshot: true,
                quality: 0.5,
              },
            }
        })
        window._slardar(&apos;start&apos;)
      </script><script>
      (function () {
        function initPerformanceObserver() {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        for (const entry of entries) {
          if (entry.name === &apos;first-paint&apos;) {
            window.parent.postMessage(
              {
                action: &apos;RENDERER:Performance.FirstPaint&apos;
              },
              &apos;*&apos;
            );
          }
          if (entry.name === &apos;first-contentful-paint&apos;) {
            window.parent.postMessage(
              {
                action: &apos;RENDERER:Performance.FirstContentfulPaint&apos;
              },
              &apos;*&apos;
            );
          }
        }
      });
    
      observer.observe({ type: &apos;paint&apos;, buffered: true });
    }
        initPerformanceObserver()
      })()
      </script><script defer src="//lf-flow-web-cdn.doubao.com/obj/flow-doubao/html_preview/*********/static/js/lib-polyfill.99626a2f.js" crossorigin="anonymous"></script><script defer src="//lf-flow-web-cdn.doubao.com/obj/flow-doubao/html_preview/*********/static/js/850.298332d1.js" crossorigin="anonymous"></script><script defer src="//lf-flow-web-cdn.doubao.com/obj/flow-doubao/html_preview/*********/static/js/renderer.b9332903.js" crossorigin="anonymous"></script><link href="//lf-flow-web-cdn.doubao.com/obj/flow-doubao/html_preview/*********/static/css/renderer.5b8138b4.css" rel="stylesheet" crossorigin="anonymous"></head>
      <body>
        <html lang="zh-CN" data-doubao-line="2" data-doubao-column="1"><head data-doubao-line="3" data-doubao-column="1">
      <meta charset="UTF-8" data-doubao-line="4" data-doubao-column="3">
      <meta name="viewport" content="width=device-width, initial-scale=1.0" data-doubao-line="5" data-doubao-column="3">
      <title data-doubao-line="6" data-doubao-column="3">客舱设施与服务五力评价系统</title>
      <script src="https://cdn.tailwindcss.com" data-doubao-line="7" data-doubao-column="3"></script>
      <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet" data-doubao-line="8" data-doubao-column="3">
      <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js" data-doubao-line="9" data-doubao-column="3"></script>
      <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js" data-doubao-line="10" data-doubao-column="3"></script>
      
      <script data-doubao-line="12" data-doubao-column="3">
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: &apos;#0f3460&apos;,
                secondary: &apos;#1a508b&apos;,
                accent: &apos;#3282b8&apos;,
                light: &apos;#eef2f5&apos;,
                success: &apos;#27ae60&apos;,
                warning: &apos;#f39c12&apos;,
                danger: &apos;#e74c3c&apos;,
              },
              fontFamily: {
                sans: [&apos;Inter&apos;, &apos;system-ui&apos;, &apos;sans-serif&apos;],
              },
            },
          }
        }
      </script>
      
      <style type="text/tailwindcss" data-doubao-line="33" data-doubao-column="3">
        @layer utilities {
          .content-auto {
            content-visibility: auto;
          }
          .airplane-animation {
            animation: fly 3s infinite ease-in-out;
          }
          .score-tooltip {
            @apply invisible absolute bg-primary text-white text-xs rounded py-1 px-2 -mt-8 -ml-4 opacity-0 transition-opacity duration-200;
          }
          .slider-container:hover .score-tooltip {
            @apply visible opacity-100;
          }
          .card-hover {
            @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
          }
          .btn-primary {
            @apply bg-primary text-white py-2 px-6 rounded-md transition-all duration-200 hover:bg-secondary hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed;
          }
          .btn-secondary {
            @apply bg-light text-primary py-2 px-6 rounded-md transition-all duration-200 hover:bg-gray-200 hover:shadow-md;
          }
          .panel-active {
            @apply border-accent bg-blue-50;
          }
        }
        
        @keyframes fly {
          0% { transform: translateX(-10px) rotate(-5deg); }
          50% { transform: translateX(10px) rotate(5deg); }
          100% { transform: translateX(-10px) rotate(-5deg); }
        }
      </style>
    <style>:root {
        
        }
      img[data-key]:not([data-review-status=&apos;0&apos;]) { object-fit: cover !important; }
      </style></head>
    <body class="bg-gray-50 font-sans" data-doubao-line="68" data-doubao-column="1">
      <div id="app" class="min-h-screen flex flex-col" data-doubao-line="69" data-doubao-column="3" data-doubao-key="0">
        <!-- 导航栏 -->
        <header class="bg-primary text-white shadow-md" data-doubao-line="71" data-doubao-column="5" data-doubao-key="1">
          <div class="container mx-auto px-4 py-3 flex justify-between items-center" data-doubao-line="72" data-doubao-column="7" data-doubao-key="2">
            <div class="flex items-center" data-doubao-line="73" data-doubao-column="9" data-doubao-key="3">
              <i class="fa fa-plane text-2xl mr-3 airplane-animation" data-doubao-line="74" data-doubao-column="11" data-doubao-key="4"></i>
              <h1 class="text-xl md:text-2xl font-bold" data-doubao-line="75" data-doubao-column="11" data-doubao-key="5">客舱设施与服务五力评价系统</h1>
            </div>
            <div id="nav-actions" class="hidden" data-doubao-line="77" data-doubao-column="9" data-doubao-key="6">
              <button id="back-btn" class="btn-secondary text-sm py-1 px-4" data-doubao-line="78" data-doubao-column="11" data-doubao-key="7">
                <i class="fa fa-arrow-left mr-1" data-doubao-line="79" data-doubao-column="13" data-doubao-key="8"></i> 返回
              </button>
            </div>
          </div>
        </header>
    
        <!-- 主内容区 -->
        <main class="flex-grow container mx-auto px-4 py-8" data-doubao-line="86" data-doubao-column="5" data-doubao-key="9">
          <!-- 界面1: 身份选择界面 -->
          <div id="page1" class="page-content" data-doubao-line="88" data-doubao-column="7" data-doubao-key="10">
            <div class="max-w-3xl mx-auto text-center mb-12" data-doubao-line="89" data-doubao-column="9" data-doubao-key="11">
              <h2 class="text-3xl font-bold text-primary mb-8" data-doubao-line="90" data-doubao-column="11" data-doubao-key="12">请选择您的评价身份</h2>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6" data-doubao-line="91" data-doubao-column="11" data-doubao-key="13">
                <button id="student-btn" class="bg-white rounded-xl shadow p-6 card-hover" data-role="student" data-weight="10%" data-doubao-line="92" data-doubao-column="13" data-doubao-key="14">
                  <div class="text-4xl mb-3 text-accent" data-doubao-line="93" data-doubao-column="15" data-doubao-key="15">
                    <i class="fa fa-graduation-cap" data-doubao-line="94" data-doubao-column="17" data-doubao-key="16"></i>
                  </div>
                  <h3 class="text-xl font-semibold mb-1" data-doubao-line="96" data-doubao-column="15" data-doubao-key="17">学生评价</h3>
                  <p class="text-gray-600" data-doubao-line="97" data-doubao-column="15" data-doubao-key="18">在总分中的占比：10%</p>
                </button>
                <button id="teacher-btn" class="bg-white rounded-xl shadow p-6 card-hover" data-role="teacher" data-weight="50%" data-doubao-line="99" data-doubao-column="13" data-doubao-key="19">
                  <div class="text-4xl mb-3 text-accent" data-doubao-line="100" data-doubao-column="15" data-doubao-key="20">
                    <i class="fa fa-book" data-doubao-line="101" data-doubao-column="17" data-doubao-key="21"></i>
                  </div>
                  <h3 class="text-xl font-semibold mb-1" data-doubao-line="103" data-doubao-column="15" data-doubao-key="22">教师评价</h3>
                  <p class="text-gray-600" data-doubao-line="104" data-doubao-column="15" data-doubao-key="23">在总分中的占比：50%</p>
                </button>
                <button id="enterprise-btn" class="bg-white rounded-xl shadow p-6 card-hover" data-role="enterprise" data-weight="40%" data-doubao-line="106" data-doubao-column="13" data-doubao-key="24">
                  <div class="text-4xl mb-3 text-accent" data-doubao-line="107" data-doubao-column="15" data-doubao-key="25">
                    <i class="fa fa-building" data-doubao-line="108" data-doubao-column="17" data-doubao-key="26"></i>
                  </div>
                  <h3 class="text-xl font-semibold mb-1" data-doubao-line="110" data-doubao-column="15" data-doubao-key="27">企业评价</h3>
                  <p class="text-gray-600" data-doubao-line="111" data-doubao-column="15" data-doubao-key="28">在总分中的占比：40%</p>
                </button>
              </div>
              
              <div class="mt-12" data-doubao-line="115" data-doubao-column="11" data-doubao-key="29">
                <button id="view-scores-btn" class="btn-primary" data-doubao-line="116" data-doubao-column="13" data-doubao-key="30">
                  <i class="fa fa-bar-chart mr-1" data-doubao-line="117" data-doubao-column="15" data-doubao-key="31"></i> 查看成绩
                </button>
              </div>
            </div>
          </div>
    
          <!-- 界面2: 学生选择界面 -->
          <div id="page2" class="page-content hidden" data-doubao-line="124" data-doubao-column="7" data-doubao-key="32">
            <div class="max-w-5xl mx-auto" data-doubao-line="125" data-doubao-column="9" data-doubao-key="33">
              <div class="flex justify-between items-center mb-6" data-doubao-line="126" data-doubao-column="11" data-doubao-key="34">
                <h2 class="text-2xl font-bold text-primary" id="page2-title" data-doubao-line="127" data-doubao-column="13" data-doubao-key="35">选择学生</h2>
                <div class="flex space-x-3" data-doubao-line="128" data-doubao-column="13" data-doubao-key="36">
                  <div class="relative" data-doubao-line="129" data-doubao-column="15" data-doubao-key="37">
                    <input type="text" id="student-search" placeholder="搜索学生..." class="pl-9 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50" data-doubao-line="130" data-doubao-column="17" data-doubao-key="38">
                    <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" data-doubao-line="132" data-doubao-column="17" data-doubao-key="39"></i>
                  </div>
                </div>
              </div>
              
              <div class="bg-white rounded-lg shadow p-4 mb-6" data-doubao-line="137" data-doubao-column="11" data-doubao-key="40">
                <div class="flex flex-wrap gap-2" id="selected-students" data-doubao-line="138" data-doubao-column="13" data-doubao-key="41">
                  <!-- 选中的学生会显示在这里 -->
                </div>
              </div>
              
              <div class="bg-white rounded-lg shadow p-4" data-doubao-line="143" data-doubao-column="11" data-doubao-key="42">
                <div class="overflow-x-auto" data-doubao-line="144" data-doubao-column="13" data-doubao-key="43">
                  <table class="min-w-full" data-doubao-line="145" data-doubao-column="15" data-doubao-key="44">
                    <thead data-doubao-line="146" data-doubao-column="17" data-doubao-key="45">
                      <tr class="bg-gray-100 text-left" data-doubao-line="147" data-doubao-column="19" data-doubao-key="46">
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="148" data-doubao-column="21" data-doubao-key="47">选择</th>
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="149" data-doubao-column="21" data-doubao-key="48">学号</th>
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="150" data-doubao-column="21" data-doubao-key="49">姓名</th>
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="151" data-doubao-column="21" data-doubao-key="50">评价状态</th>
                      </tr>
                    </thead>
                    <tbody id="student-list" data-doubao-line="154" data-doubao-column="17" data-doubao-key="51">
                      <!-- 学生列表会动态生成在这里 -->
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div class="mt-6 flex justify-between" data-doubao-line="161" data-doubao-column="11" data-doubao-key="52">
                <button id="page2-back-btn" class="btn-secondary" data-doubao-line="162" data-doubao-column="13" data-doubao-key="53">
                  <i class="fa fa-arrow-left mr-1" data-doubao-line="163" data-doubao-column="15" data-doubao-key="54"></i> 返回
                </button>
                <button id="start-evaluation-btn" class="btn-primary" disabled="" data-doubao-line="165" data-doubao-column="13" data-doubao-key="55">
                  开始评价 <i class="fa fa-arrow-right ml-1" data-doubao-line="166" data-doubao-column="20" data-doubao-key="56"></i>
                </button>
              </div>
            </div>
          </div>
    
          <!-- 界面3: 评价打分界面 -->
          <div id="page3" class="page-content hidden" data-doubao-line="173" data-doubao-column="7" data-doubao-key="57">
            <div class="max-w-4xl mx-auto" data-doubao-line="174" data-doubao-column="9" data-doubao-key="58">
              <div class="flex justify-between items-center mb-6" data-doubao-line="175" data-doubao-column="11" data-doubao-key="59">
                <h2 class="text-2xl font-bold text-primary" id="evaluation-title" data-doubao-line="176" data-doubao-column="13" data-doubao-key="60">评价打分</h2>
                <div class="text-sm bg-gray-100 rounded-full px-4 py-1" id="evaluation-progress" data-doubao-line="177" data-doubao-column="13" data-doubao-key="61">
                  进度：0/5 维度
                </div>
              </div>
              
              <div class="bg-white rounded-lg shadow p-6 mb-6" data-doubao-line="182" data-doubao-column="11" data-doubao-key="62">
                <div class="flex items-center justify-between mb-4" data-doubao-line="183" data-doubao-column="13" data-doubao-key="63">
                  <h3 class="text-lg font-semibold" id="current-student-info" data-doubao-line="184" data-doubao-column="15" data-doubao-key="64">教师评价 - 于东来</h3>
                  <div class="flex items-center text-sm text-gray-500" data-doubao-line="185" data-doubao-column="15" data-doubao-key="65">
                    <i class="fa fa-save mr-1" data-doubao-line="186" data-doubao-column="17" data-doubao-key="66"></i> <span id="save-status" data-doubao-line="186" data-doubao-column="49" data-doubao-key="67">已保存</span>
                  </div>
                </div>
                
                <!-- 维度评价面板 -->
                <div id="dimensions-container" data-doubao-line="191" data-doubao-column="13" data-doubao-key="68">
                  <!-- 维度面板会动态生成在这里 -->
                </div>
              </div>
              
              <div class="mt-6 flex justify-between" data-doubao-line="196" data-doubao-column="11" data-doubao-key="69">
                <button id="page3-back-btn" class="btn-secondary" data-doubao-line="197" data-doubao-column="13" data-doubao-key="70">
                  <i class="fa fa-arrow-left mr-1" data-doubao-line="198" data-doubao-column="15" data-doubao-key="71"></i> 返回
                </button>
                <button id="submit-evaluation-btn" class="btn-primary" disabled="" data-doubao-line="200" data-doubao-column="13" data-doubao-key="72">
                  提交评价 <i class="fa fa-check ml-1" data-doubao-line="201" data-doubao-column="20" data-doubao-key="73"></i>
                </button>
              </div>
            </div>
          </div>
    
          <!-- 界面4: 成绩展示界面 -->
          <div id="page4" class="page-content hidden" data-doubao-line="208" data-doubao-column="7" data-doubao-key="74">
            <div class="max-w-6xl mx-auto" data-doubao-line="209" data-doubao-column="9" data-doubao-key="75">
              <div class="flex justify-between items-center mb-6" data-doubao-line="210" data-doubao-column="11" data-doubao-key="76">
                <div data-doubao-line="211" data-doubao-column="13" data-doubao-key="77">
                  <h2 class="text-2xl font-bold text-primary" id="student-score-title" data-doubao-line="212" data-doubao-column="15" data-doubao-key="78">学生成绩 - 于东来</h2>
                  <p class="text-gray-600" id="student-id" data-doubao-line="213" data-doubao-column="15" data-doubao-key="79">学号：235601101</p>
                </div>
                <div class="flex space-x-3" data-doubao-line="215" data-doubao-column="13" data-doubao-key="80">
                  <button id="export-excel-btn" class="btn-primary" data-doubao-line="216" data-doubao-column="15" data-doubao-key="81">
                    <i class="fa fa-file-excel-o mr-1" data-doubao-line="217" data-doubao-column="17" data-doubao-key="82"></i> 导出表格
                  </button>
                </div>
              </div>
              
              <div class="bg-white rounded-lg shadow p-6 mb-6" data-doubao-line="222" data-doubao-column="11" data-doubao-key="83">
                <div class="flex flex-col md:flex-row gap-6" data-doubao-line="223" data-doubao-column="13" data-doubao-key="84">
                  <div class="flex-1 bg-gray-50 rounded-lg p-4" data-doubao-line="224" data-doubao-column="15" data-doubao-key="85">
                    <h3 class="text-lg font-semibold mb-4 text-center" data-doubao-line="225" data-doubao-column="17" data-doubao-key="86">综合评分</h3>
                    <div class="flex justify-center" data-doubao-line="226" data-doubao-column="17" data-doubao-key="87">
                      <div class="w-40 h-40 rounded-full bg-gradient-to-r from-blue-500 to-green-500 flex items-center justify-center" data-doubao-line="227" data-doubao-column="19" data-doubao-key="88">
                        <div class="text-center" data-doubao-line="228" data-doubao-column="21" data-doubao-key="89">
                          <div class="text-4xl font-bold text-white" id="total-score" data-doubao-line="229" data-doubao-column="23" data-doubao-key="90">85</div>
                          <div class="text-sm text-white/80" data-doubao-line="230" data-doubao-column="23" data-doubao-key="91">总分</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex-1" data-doubao-line="236" data-doubao-column="15" data-doubao-key="92">
                    <h3 class="text-lg font-semibold mb-4" data-doubao-line="237" data-doubao-column="17" data-doubao-key="93">评价占比</h3>
                    <div class="h-64" data-doubao-line="238" data-doubao-column="17" data-doubao-key="94">
                      <canvas id="pie-chart" data-doubao-line="239" data-doubao-column="19" data-doubao-key="95"></canvas>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6" data-doubao-line="245" data-doubao-column="11" data-doubao-key="96">
                <div class="bg-white rounded-lg shadow p-6" data-doubao-line="246" data-doubao-column="13" data-doubao-key="97">
                  <h3 class="text-lg font-semibold mb-4" data-doubao-line="247" data-doubao-column="15" data-doubao-key="98">五力雷达图</h3>
                  <div class="h-64" data-doubao-line="248" data-doubao-column="15" data-doubao-key="99">
                    <canvas id="radar-chart" data-doubao-line="249" data-doubao-column="17" data-doubao-key="100"></canvas>
                  </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6" data-doubao-line="253" data-doubao-column="13" data-doubao-key="101">
                  <h3 class="text-lg font-semibold mb-4" data-doubao-line="254" data-doubao-column="15" data-doubao-key="102">各维度平均分</h3>
                  <div class="h-64" data-doubao-line="255" data-doubao-column="15" data-doubao-key="103">
                    <canvas id="bar-chart" data-doubao-line="256" data-doubao-column="17" data-doubao-key="104"></canvas>
                  </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6 md:col-span-2" data-doubao-line="260" data-doubao-column="13" data-doubao-key="105">
                  <h3 class="text-lg font-semibold mb-4" data-doubao-line="261" data-doubao-column="15" data-doubao-key="106">各维度得分趋势</h3>
                  <div class="h-64" data-doubao-line="262" data-doubao-column="15" data-doubao-key="107">
                    <canvas id="line-chart" data-doubao-line="263" data-doubao-column="17" data-doubao-key="108"></canvas>
                  </div>
                </div>
              </div>
              
              <div class="bg-white rounded-lg shadow p-6 mb-6" data-doubao-line="268" data-doubao-column="11" data-doubao-key="109">
                <h3 class="text-lg font-semibold mb-4" data-doubao-line="269" data-doubao-column="13" data-doubao-key="110">详细评分</h3>
                <div class="overflow-x-auto" data-doubao-line="270" data-doubao-column="13" data-doubao-key="111">
                  <table class="min-w-full" data-doubao-line="271" data-doubao-column="15" data-doubao-key="112">
                    <thead data-doubao-line="272" data-doubao-column="17" data-doubao-key="113">
                      <tr class="bg-gray-100 text-left" data-doubao-line="273" data-doubao-column="19" data-doubao-key="114">
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="274" data-doubao-column="21" data-doubao-key="115">维度</th>
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="275" data-doubao-column="21" data-doubao-key="116">学生评分 (10%)</th>
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="276" data-doubao-column="21" data-doubao-key="117">教师评分 (50%)</th>
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="277" data-doubao-column="21" data-doubao-key="118">企业评分 (40%)</th>
                        <th class="py-3 px-4 font-semibold text-gray-700" data-doubao-line="278" data-doubao-column="21" data-doubao-key="119">维度平均分</th>
                      </tr>
                    </thead>
                    <tbody id="detail-scores-table" data-doubao-line="281" data-doubao-column="17" data-doubao-key="120">
                      <!-- 详细评分表格会动态生成在这里 -->
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div class="mt-6 flex justify-between" data-doubao-line="288" data-doubao-column="11" data-doubao-key="121">
                <button id="page4-back-btn" class="btn-secondary" data-doubao-line="289" data-doubao-column="13" data-doubao-key="122">
                  <i class="fa fa-arrow-left mr-1" data-doubao-line="290" data-doubao-column="15" data-doubao-key="123"></i> 返回
                </button>
                <button id="page4-home-btn" class="btn-secondary" data-doubao-line="292" data-doubao-column="13" data-doubao-key="124">
                  <i class="fa fa-home mr-1" data-doubao-line="293" data-doubao-column="15" data-doubao-key="125"></i> 返回首页
                </button>
              </div>
            </div>
          </div>
        </main>
    
        <!-- 页脚 -->
        <footer class="bg-primary text-white py-4 mt-auto" data-doubao-line="301" data-doubao-column="5" data-doubao-key="126">
          <div class="container mx-auto px-4 text-center text-sm" data-doubao-line="302" data-doubao-column="7" data-doubao-key="127">
            <p data-doubao-line="303" data-doubao-column="9" data-doubao-key="128">© 2025 客舱设施与服务五力评价系统 | 航空职业教育评价平台</p>
          </div>
        </footer>
      </div>
    
      <script data-doubao-line="308" data-doubao-column="3" data-doubao-key="129">
        // 学生数据
        const students = [
          { id: &apos;235601101&apos;, name: &apos;于东来&apos; },
          { id: &apos;235601102&apos;, name: &apos;鲁旋&apos; },
          { id: &apos;235601103&apos;, name: &apos;杨志城&apos; },
          { id: &apos;235601104&apos;, name: &apos;陶港&apos; },
          { id: &apos;235601105&apos;, name: &apos;张建平&apos; },
          { id: &apos;235601106&apos;, name: &apos;李伟贤&apos; },
          { id: &apos;235601108&apos;, name: &apos;甘甜&apos; },
          { id: &apos;235601109&apos;, name: &apos;林佳薇&apos; },
          { id: &apos;235601112&apos;, name: &apos;陈芷琳&apos; },
          { id: &apos;235601113&apos;, name: &apos;陈心璇&apos; },
          { id: &apos;235601114&apos;, name: &apos;吴洪锐&apos; },
          { id: &apos;235601115&apos;, name: &apos;黄伟华&apos; },
          { id: &apos;235601116&apos;, name: &apos;池文慧&apos; },
          { id: &apos;235601117&apos;, name: &apos;黄梦馨&apos; },
          { id: &apos;235601118&apos;, name: &apos;蔡小东&apos; },
          { id: &apos;235601119&apos;, name: &apos;朱水霞&apos; },
          { id: &apos;235601120&apos;, name: &apos;郑琳敏&apos; },
          { id: &apos;235601121&apos;, name: &apos;李婧阳&apos; },
          { id: &apos;235601122&apos;, name: &apos;吴佳萍&apos; },
          { id: &apos;235601123&apos;, name: &apos;庄文烨&apos; },
          { id: &apos;235601124&apos;, name: &apos;黄晓洲&apos; },
          { id: &apos;235601125&apos;, name: &apos;曾妍妍&apos; },
          { id: &apos;235601126&apos;, name: &apos;康江梅&apos; },
          { id: &apos;235601127&apos;, name: &apos;王秋婷&apos; },
          { id: &apos;235601128&apos;, name: &apos;陈良虔&apos; },
          { id: &apos;235601130&apos;, name: &apos;黄宝仪&apos; },
          { id: &apos;235601131&apos;, name: &apos;陈妍馨&apos; },
          { id: &apos;235601133&apos;, name: &apos;曾欣云&apos; },
          { id: &apos;235601134&apos;, name: &apos;许佳婷&apos; },
          { id: &apos;235601135&apos;, name: &apos;胡亦赫&apos; },
          { id: &apos;235601136&apos;, name: &apos;廖一涵&apos; },
          { id: &apos;235601137&apos;, name: &apos;元国美&apos; },
          { id: &apos;235601140&apos;, name: &apos;钟玉芬&apos; },
          { id: &apos;235601141&apos;, name: &apos;姜以欣&apos; },
          { id: &apos;235601142&apos;, name: &apos;黄炘炘&apos; },
          { id: &apos;235601143&apos;, name: &apos;张熠平&apos; },
          { id: &apos;235601144&apos;, name: &apos;罗家炎&apos; },
          { id: &apos;235601145&apos;, name: &apos;黄雯婷&apos; },
          { id: &apos;235601147&apos;, name: &apos;赖庆生&apos; },
          { id: &apos;235601148&apos;, name: &apos;曾毅&apos; },
          { id: &apos;235601149&apos;, name: &apos;张凯翔&apos; },
          { id: &apos;235601152&apos;, name: &apos;吉鹏宇&apos; },
          { id: &apos;235601153&apos;, name: &apos;崔方烨&apos; },
          { id: &apos;235601154&apos;, name: &apos;庄王宇宁&apos; },
          { id: &apos;235601157&apos;, name: &apos;曾燕&apos; },
          { id: &apos;215601110&apos;, name: &apos;杨天云&apos; },
        ];
    
        // 评价维度数据
        const evaluationDimensions = [
          {
            id: &apos;management&apos;,
            name: &apos;管理力&apos;,
            indicators: [
              { id: &apos;m1&apos;, name: &apos;全流程完整性&apos;, criteria: &apos;80～100分：全流程无缝衔接，文化适配精准；61～80分：流程完整，协作略迟滞；41～60分：遗漏1环节，文化元素生硬；21～40分：流程断裂，无文化整合；0～20：未完成&apos; },
              { id: &apos;m2&apos;, name: &apos;跨岗位协作&apos;, criteria: &apos;80～100分：全流程无缝衔接，文化适配精准；61～80分：流程完整，协作略迟滞；41～60分：遗漏1环节，文化元素生硬；21～40分：流程断裂，无文化整合；0～20：未完成&apos; },
              { id: &apos;m3&apos;, name: &apos;文化资源整合&apos;, criteria: &apos;80～100分：全流程无缝衔接，文化适配精准；61～80分：流程完整，协作略迟滞；41～60分：遗漏1环节，文化元素生硬；21～40分：流程断裂，无文化整合；0～20：未完成&apos; },
              { id: &apos;m4&apos;, name: &apos;时效管理&apos;, criteria: &apos;80～100分：全流程无缝衔接，文化适配精准；61～80分：流程完整，协作略迟滞；41～60分：遗漏1环节，文化元素生硬；21～40分：流程断裂，无文化整合；0～20：未完成&apos; }
            ]
          },
          {
            id: &apos;execution&apos;,
            name: &apos;执行力&apos;,
            indicators: [
              { id: &apos;e1&apos;, name: &apos;动作标准化&apos;, criteria: &apos;80～100分：动作精准高效，零失误；61～80分：操作规范，轻微延迟；41～60分：1处错误/超时；21～40分：多次错误/超时；0～20：引发安全隐患&apos; },
              { id: &apos;e2&apos;, name: &apos;设备使用&apos;, criteria: &apos;80～100分：动作精准高效，零失误；61～80分：操作规范，轻微延迟；41～60分：1处错误/超时；21～40分：多次错误/超时；0～20：引发安全隐患&apos; },
              { id: &apos;e3&apos;, name: &apos;时效性&apos;, criteria: &apos;80～100分：动作精准高效，零失误；61～80分：操作规范，轻微延迟；41～60分：1处错误/超时；21～40分：多次错误/超时；0～20：引发安全隐患&apos; },
              { id: &apos;e4&apos;, name: &apos;细节处理&apos;, criteria: &apos;80～100分：动作精准高效，零失误；61～80分：操作规范，轻微延迟；41～60分：1处错误/超时；21～40分：多次错误/超时；0～20：引发安全隐患&apos; }
            ]
          },
          {
            id: &apos;observation&apos;,
            name: &apos;观察力&apos;,
            indicators: [
              { id: &apos;o1&apos;, name: &apos;显性需求响应&apos;, criteria: &apos;80～100分：隐性需求识别率≥90%；61～80分：显性需求全满足，隐性需求识别1处；41～60分：仅响应显性需求；21～40分：忽视关键行为信号；0～20分：需求误判&apos; },
              { id: &apos;o2&apos;, name: &apos;隐性需求洞察&apos;, criteria: &apos;80～100分：隐性需求识别率≥90%；61～80分：显性需求全满足，隐性需求识别1处；41～60分：仅响应显性需求；21～40分：忽视关键行为信号；0～20分：需求误判&apos; },
              { id: &apos;o3&apos;, name: &apos;风险预判&apos;, criteria: &apos;80～100分：隐性需求识别率≥90%；61～80分：显性需求全满足，隐性需求识别1处；41～60分：仅响应显性需求；21～40分：忽视关键行为信号；0～20分：需求误判&apos; },
              { id: &apos;o4&apos;, name: &apos;文化敏感度&apos;, criteria: &apos;80～100分：隐性需求识别率≥90%；61～80分：显性需求全满足，隐性需求识别1处；41～60分：仅响应显性需求；21～40分：忽视关键行为信号；0～20分：需求误判&apos; }
            ]
          },
          {
            id: &apos;communication&apos;,
            name: &apos;沟通力&apos;,
            indicators: [
              { id: &apos;c1&apos;, name: &apos;语言策略&apos;, criteria: &apos;80～100分：方言破冰成功，无禁忌用语；61～80分：普通话沟通流畅，非语言到位；41～60分：沟通机械，偶有禁忌词；21～40分：语言不当引发不适；0～20分：沟通中断&apos; },
              { id: &apos;c2&apos;, name: &apos;非语言技巧&apos;, criteria: &apos;80～100分：方言破冰成功，无禁忌用语；61～80分：普通话沟通流畅，非语言到位；41～60分：沟通机械，偶有禁忌词；21～40分：语言不当引发不适；0～20分：沟通中断&apos; },
              { id: &apos;c3&apos;, name: &apos;方言应用&apos;, criteria: &apos;80～100分：方言破冰成功，无禁忌用语；61～80分：普通话沟通流畅，非语言到位；41～60分：沟通机械，偶有禁忌词；21～40分：语言不当引发不适；0～20分：沟通中断&apos; },
              { id: &apos;c4&apos;, name: &apos;禁忌规避&apos;, criteria: &apos;80～100分：方言破冰成功，无禁忌用语；61～80分：普通话沟通流畅，非语言到位；41～60分：沟通机械，偶有禁忌词；21～40分：语言不当引发不适；0～20分：沟通中断&apos; }
            ]
          },
          {
            id: &apos;adaptability&apos;,
            name: &apos;应变力&apos;,
            indicators: [
              { id: &apos;a1&apos;, name: &apos;流程异常处理&apos;, criteria: &apos;80～100分：创新策略化解危机；61～80分：按预案处理，结果平稳；41～60分：处理延迟，旅客轻微不满；21～40分：依赖他人介入；0～20分：应对失当激化矛盾&apos; },
              { id: &apos;a2&apos;, name: &apos;冲突化解&apos;, criteria: &apos;80～100分：创新策略化解危机；61～80分：按预案处理，结果平稳；41～60分：处理延迟，旅客轻微不满；21～40分：依赖他人介入；0～20分：应对失当激化矛盾&apos; },
              { id: &apos;a3&apos;, name: &apos;极端案例应对&apos;, criteria: &apos;80～100分：创新策略化解危机；61～80分：按预案处理，结果平稳；41～60分：处理延迟，旅客轻微不满；21～40分：依赖他人介入；0～20分：应对失当激化矛盾&apos; },
              { id: &apos;a4&apos;, name: &apos;文化冲突调解&apos;, criteria: &apos;80～100分：创新策略化解危机；61～80分：按预案处理，结果平稳；41～60分：处理延迟，旅客轻微不满；21～40分：依赖他人介入；0～20分：应对失当激化矛盾&apos; }
            ]
          }
        ];
    
        // 全局状态
        const state = {
          currentPage: 1,
          selectedRole: null,
          selectedStudentId: null,
          isViewingScores: false,
          currentDimension: 0,
          evaluationData: {}
        };
    
        // DOM元素
        const pages = {
          1: document.getElementById(&apos;page1&apos;),
          2: document.getElementById(&apos;page2&apos;),
          3: document.getElementById(&apos;page3&apos;),
          4: document.getElementById(&apos;page4&apos;)
        };
    
        // 页面初始化
        function initApp() {
          loadEvaluationData();
          setupEventListeners();
          renderStudentList();
        }
    
        // 加载评价数据
        function loadEvaluationData() {
          const savedData = localStorage.getItem(&apos;evaluationData&apos;);
          if (savedData) {
            state.evaluationData = JSON.parse(savedData);
          } else {
            // 初始化评价数据结构
            students.forEach(student => {
              state.evaluationData[student.id] = {
                name: student.name,
                scores: {
                  student: {
                    management: { m1: 0, m2: 0, m3: 0, m4: 0, completed: false },
                    execution: { e1: 0, e2: 0, e3: 0, e4: 0, completed: false },
                    observation: { o1: 0, o2: 0, o3: 0, o4: 0, completed: false },
                    communication: { c1: 0, c2: 0, c3: 0, c4: 0, completed: false },
                    adaptability: { a1: 0, a2: 0, a3: 0, a4: 0, completed: false }
                  },
                  teacher: {
                    management: { m1: 0, m2: 0, m3: 0, m4: 0, completed: false },
                    execution: { e1: 0, e2: 0, e3: 0, e4: 0, completed: false },
                    observation: { o1: 0, o2: 0, o3: 0, o4: 0, completed: false },
                    communication: { c1: 0, c2: 0, c3: 0, c4: 0, completed: false },
                    adaptability: { a1: 0, a2: 0, a3: 0, a4: 0, completed: false }
                  },
                  enterprise: {
                    management: { m1: 0, m2: 0, m3: 0, m4: 0, completed: false },
                    execution: { e1: 0, e2: 0, e3: 0, e4: 0, completed: false },
                    observation: { o1: 0, o2: 0, o3: 0, o4: 0, completed: false },
                    communication: { c1: 0, c2: 0, c3: 0, c4: 0, completed: false },
                    adaptability: { a1: 0, a2: 0, a3: 0, a4: 0, completed: false }
                  }
                },
                status: {
                  student: &apos;未评价&apos;,
                  teacher: &apos;未评价&apos;,
                  enterprise: &apos;未评价&apos;
                }
              };
            });
            saveEvaluationData();
          }
        }
    
        // 保存评价数据
        function saveEvaluationData() {
          localStorage.setItem(&apos;evaluationData&apos;, JSON.stringify(state.evaluationData));
        }
    
        // 设置事件监听器
        function setupEventListeners() {
          // 界面1: 身份选择按钮
          document.getElementById(&apos;student-btn&apos;).addEventListener(&apos;click&apos;, () => selectRole(&apos;student&apos;));
          document.getElementById(&apos;teacher-btn&apos;).addEventListener(&apos;click&apos;, () => selectRole(&apos;teacher&apos;));
          document.getElementById(&apos;enterprise-btn&apos;).addEventListener(&apos;click&apos;, () => selectRole(&apos;enterprise&apos;));
          document.getElementById(&apos;view-scores-btn&apos;).addEventListener(&apos;click&apos;, viewScores);
    
          // 界面2: 学生选择
          document.getElementById(&apos;student-search&apos;).addEventListener(&apos;input&apos;, filterStudents);
          document.getElementById(&apos;page2-back-btn&apos;).addEventListener(&apos;click&apos;, goToPage1);
          document.getElementById(&apos;start-evaluation-btn&apos;).addEventListener(&apos;click&apos;, goToPage3);
    
          // 界面3: 评价打分
          document.getElementById(&apos;page3-back-btn&apos;).addEventListener(&apos;click&apos;, goToPage2);
          document.getElementById(&apos;submit-evaluation-btn&apos;).addEventListener(&apos;click&apos;, submitEvaluation);
    
          // 界面4: 成绩展示
          document.getElementById(&apos;page4-back-btn&apos;).addEventListener(&apos;click&apos;, goToPage2);
          document.getElementById(&apos;page4-home-btn&apos;).addEventListener(&apos;click&apos;, goToPage1);
          document.getElementById(&apos;export-excel-btn&apos;).addEventListener(&apos;click&apos;, exportToExcel);
        }
    
        // 选择身份
        function selectRole(role) {
          state.selectedRole = role;
          state.isViewingScores = false;
          goToPage2();
        }
    
        // 查看成绩
        function viewScores() {
          state.isViewingScores = true;
          goToPage2();
        }
    
        // 切换页面
        function showPage(pageNum) {
          Object.keys(pages).forEach(key => {
            pages[key].classList.add(&apos;hidden&apos;);
          });
          pages[pageNum].classList.remove(&apos;hidden&apos;);
          state.currentPage = pageNum;
    
          // 显示/隐藏导航栏中的返回按钮
          if (pageNum === 1) {
            document.getElementById(&apos;nav-actions&apos;).classList.add(&apos;hidden&apos;);
          } else {
            document.getElementById(&apos;nav-actions&apos;).classList.remove(&apos;hidden&apos;);
            document.getElementById(&apos;back-btn&apos;).onclick = () => {
              if (pageNum === 2) {
                goToPage1();
              } else if (pageNum === 3) {
                goToPage2();
              } else if (pageNum === 4) {
                if (state.isViewingScores) {
                  goToPage2();
                } else {
                  goToPage1();
                }
              }
            };
          }
    
          // 根据当前页面执行相应的初始化操作
          if (pageNum === 2) {
            initPage2();
          } else if (pageNum === 3) {
            initPage3();
          } else if (pageNum === 4) {
            initPage4();
          }
        }
    
        // 前往页面1
        function goToPage1() {
          state.selectedRole = null;
          state.selectedStudentId = null;
          showPage(1);
        }
    
        // 前往页面2
        function goToPage2() {
          showPage(2);
        }
    
        // 前往页面3
        function goToPage3() {
          if (state.selectedRole &amp;&amp; state.selectedStudentId) {
            showPage(3);
          }
        }
    
        // 前往页面4
        function goToPage4() {
          if (state.selectedStudentId) {
            showPage(4);
          }
        }
    
        // 初始化页面2
        function initPage2() {
          // 更新页面标题
          if (state.isViewingScores) {
            document.getElementById(&apos;page2-title&apos;).textContent = &apos;查看学生成绩&apos;;
            document.getElementById(&apos;start-evaluation-btn&apos;).classList.add(&apos;hidden&apos;);
          } else {
            document.getElementById(&apos;page2-title&apos;).textContent = &apos;选择学生&apos;;
            document.getElementById(&apos;start-evaluation-btn&apos;).classList.remove(&apos;hidden&apos;);
            
            // 根据是否选择了身份和学生来启用/禁用"开始评价"按钮
            document.getElementById(&apos;start-evaluation-btn&apos;).disabled = !(state.selectedRole &amp;&amp; state.selectedStudentId);
          }
          
          // 渲染学生列表
          renderStudentList();
        }
    
        // 渲染学生列表
        function renderStudentList() {
          const container = document.getElementById(&apos;student-list&apos;);
          container.innerHTML = &apos;&apos;;
          
          // 对学生按学号排序
          const sortedStudents = [...students].sort((a, b) => a.id.localeCompare(b.id));
          
          sortedStudents.forEach(student => {
            const row = document.createElement(&apos;tr&apos;);
            row.className = &apos;border-b hover:bg-gray-50 transition-colors&apos;;
            
            // 获取评价状态
            const status = state.evaluationData[student.id]?.status[state.selectedRole] || &apos;未评价&apos;;
            let statusClass = &apos;text-gray-500&apos;;
            if (status === &apos;已完成&apos;) statusClass = &apos;text-green-500&apos;;
            else if (status === &apos;进行中&apos;) statusClass = &apos;text-yellow-500&apos;;
            
            // 构建表格行
            row.innerHTML = `
              <td class="py-3 px-4">
                <div class="flex items-center">
                  <input type="checkbox" id="student-${student.id}" 
                         class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                         ${state.isViewingScores ? &apos;disabled&apos; : &apos;&apos;}
                         ${state.selectedStudentId === student.id ? &apos;checked&apos; : &apos;&apos;}>
                  <label for="student-${student.id}" class="ml-2 text-sm text-gray-700"></label>
                </div>
              </td>
              <td class="py-3 px-4 font-medium text-gray-900">${student.id}</td>
              <td class="py-3 px-4">${student.name}</td>
              <td class="py-3 px-4">
                <span class="${statusClass}">${status}</span>
              </td>
            `;
            
            // 添加点击事件
            const checkbox = row.querySelector(`#student-${student.id}`);
            const handleStudentSelect = () => {
              if (state.isViewingScores) {
                // 查看成绩模式下，点击直接查看该学生的成绩
                state.selectedStudentId = student.id;
                goToPage4();
              } else {
                // 评价模式下，处理单选逻辑
                if (checkbox.checked) {
                  // 取消其他所有已选中的学生
                  document.querySelectorAll(&apos;#student-list input[type="checkbox"]:checked&apos;).forEach(el => {
                    if (el.id !== `student-${student.id}`) {
                      el.checked = false;
                    }
                  });
                  state.selectedStudentId = student.id;
                } else {
                  state.selectedStudentId = null;
                }
                
                // 更新已选学生标签
                updateSelectedStudents();
                
                // 启用/禁用"开始评价"按钮
                document.getElementById(&apos;start-evaluation-btn&apos;).disabled = !(state.selectedRole &amp;&amp; state.selectedStudentId);
              }
            };
            
            checkbox.addEventListener(&apos;change&apos;, handleStudentSelect);
            row.addEventListener(&apos;click&apos;, (e) => {
              if (e.target.tagName !== &apos;INPUT&apos; &amp;&amp; e.target.tagName !== &apos;LABEL&apos;) {
                checkbox.checked = !checkbox.checked;
                handleStudentSelect();
              }
            });
            
            container.appendChild(row);
          });
          
          // 更新已选学生标签
          updateSelectedStudents();
        }
    
        // 更新已选学生标签
        function updateSelectedStudents() {
          const container = document.getElementById(&apos;selected-students&apos;);
          container.innerHTML = &apos;&apos;;
          
          if (state.selectedStudentId) {
            const student = students.find(s => s.id === state.selectedStudentId);
            if (student) {
              const badge = document.createElement(&apos;div&apos;);
              badge.className = &apos;bg-primary/10 text-primary px-3 py-1 rounded-full text-sm flex items-center&apos;;
              badge.innerHTML = `
                ${student.name} (${student.id})
                <button class="ml-2 text-primary hover:text-primary/70 focus:outline-none" 
                        onclick="deselectStudent(&apos;${student.id}&apos;)">
                  <i class="fa fa-times-circle"></i>
                </button>
              `;
              container.appendChild(badge);
            }
          } else {
            const placeholder = document.createElement(&apos;div&apos;);
            placeholder.className = &apos;text-gray-500 italic&apos;;
            placeholder.textContent = &apos;尚未选择学生&apos;;
            container.appendChild(placeholder);
          }
        }
    
        // 取消选择学生
        function deselectStudent(studentId) {
          if (state.selectedStudentId === studentId) {
            state.selectedStudentId = null;
            renderStudentList();
            document.getElementById(&apos;start-evaluation-btn&apos;).disabled = !(state.selectedRole &amp;&amp; state.selectedStudentId);
          }
        }
    
        // 过滤学生列表
        function filterStudents() {
          const searchTerm = document.getElementById(&apos;student-search&apos;).value.toLowerCase();
          const rows = document.querySelectorAll(&apos;#student-list tr&apos;);
          
          rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
              row.style.display = &apos;&apos;;
            } else {
              row.style.display = &apos;none&apos;;
            }
          });
        }
    
        // 初始化页面3
        function initPage3() {
          if (!state.selectedRole || !state.selectedStudentId) return;
          
          const student = students.find(s => s.id === state.selectedStudentId);
          if (!student) return;
          
          // 更新页面标题
          const roleText = {
            student: &apos;学生评价&apos;,
            teacher: &apos;教师评价&apos;,
            enterprise: &apos;企业评价&apos;
          }[state.selectedRole];
          document.getElementById(&apos;evaluation-title&apos;).textContent = &apos;评价打分&apos;;
          document.getElementById(&apos;current-student-info&apos;).textContent = `${roleText} - ${student.name}`;
          
          // 渲染评价维度面板
          renderEvaluationPanels();
          
          // 更新进度
          updateEvaluationProgress();
          
          // 启用/禁用提交按钮
          updateSubmitButtonStatus();
        }
    
        // 渲染评价维度面板
        function renderEvaluationPanels() {
          const container = document.getElementById(&apos;dimensions-container&apos;);
          container.innerHTML = &apos;&apos;;
          
          evaluationDimensions.forEach((dimension, index) => {
            const isCurrent = index === state.currentDimension;
            const studentData = state.evaluationData[state.selectedStudentId];
            const roleScores = studentData?.scores[state.selectedRole][dimension.id] || {};
            const isCompleted = studentData?.scores[state.selectedRole][dimension.id]?.completed || false;
            
            const panel = document.createElement(&apos;div&apos;);
            panel.className = `border rounded-lg p-4 mb-4 ${isCurrent ? &apos;panel-active&apos; : &apos;&apos;}`;
            panel.innerHTML = `
              <div class="flex justify-between items-center cursor-pointer mb-4" 
                   onclick="switchDimension(${index})">
                <h4 class="text-lg font-semibold flex items-center">
                  <span class="w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center mr-2">
                    ${index + 1}
                  </span>
                  ${dimension.name}
                  ${isCompleted ? &apos;<i class="fa fa-check-circle text-success ml-2"></i>&apos; : &apos;&apos;}
                </h4>
                <div class="text-sm text-gray-500">
                  ${isCurrent ? &apos;当前维度&apos; : isCompleted ? &apos;已完成&apos; : &apos;未完成&apos;}
                </div>
              </div>
              
              <div class="dimension-content ${isCurrent ? &apos;&apos; : &apos;hidden&apos;}">
                ${dimension.indicators.map((indicator, i) => `
                  <div class="mb-6">
                    <div class="flex justify-between items-start mb-2">
                      <h5 class="font-medium">${i + 1}. ${indicator.name}</h5>
                      <div class="flex items-center">
                        <span class="score-value mr-2" id="score-${dimension.id}-${indicator.id}">
                          ${roleScores[indicator.id] || 0}
                        </span>
                        <span class="text-xs text-gray-500">分</span>
                      </div>
                    </div>
                    
                    <div class="slider-container relative mb-3">
                      <input type="range" min="0" max="100" value="${roleScores[indicator.id] || 0}" 
                             class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                             id="slider-${dimension.id}-${indicator.id}"
                             oninput="updateScore(&apos;${dimension.id}&apos;, &apos;${indicator.id}&apos;, this.value)">
                      <span class="score-tooltip" id="tooltip-${dimension.id}-${indicator.id}">
                        ${roleScores[indicator.id] || 0}
                      </span>
                    </div>
                    
                    <div class="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                      ${indicator.criteria}
                    </div>
                  </div>
                `).join(&apos;&apos;)}
              </div>
            `;
            
            container.appendChild(panel);
          });
        }
    
        // 切换维度
        function switchDimension(index) {
          state.currentDimension = index;
          renderEvaluationPanels();
        }
    
        // 更新分数
        function updateScore(dimensionId, indicatorId, value) {
          // 更新显示
          document.getElementById(`score-${dimensionId}-${indicatorId}`).textContent = value;
          document.getElementById(`tooltip-${dimensionId}-${indicatorId}`).textContent = value;
          
          // 保存分数
          if (!state.evaluationData[state.selectedStudentId]) {
            // 初始化学生数据
            const student = students.find(s => s.id === state.selectedStudentId);
            state.evaluationData[state.selectedStudentId] = {
              name: student.name,
              scores: {
                student: {},
                teacher: {},
                enterprise: {}
              },
              status: {
                student: &apos;未评价&apos;,
                teacher: &apos;未评价&apos;,
                enterprise: &apos;未评价&apos;
              }
            };
          }
          
          // 初始化角色维度数据
          if (!state.evaluationData[state.selectedStudentId].scores[state.selectedRole][dimensionId]) {
            state.evaluationData[state.selectedStudentId].scores[state.selectedRole][dimensionId] = {
              completed: false
            };
          }
          
          // 更新分数
          state.evaluationData[state.selectedStudentId].scores[state.selectedRole][dimensionId][indicatorId] = parseInt(value);
          
          // 显示保存中状态
          const saveStatus = document.getElementById(&apos;save-status&apos;);
          saveStatus.textContent = &apos;保存中...&apos;;
          saveStatus.className = &apos;text-yellow-500&apos;;
          
          // 保存到本地存储
          setTimeout(() => {
            saveEvaluationData();
            saveStatus.textContent = &apos;已保存&apos;;
            saveStatus.className = &apos;text-green-500&apos;;
            
            // 检查维度是否完成
            checkDimensionCompletion(dimensionId);
            
            // 更新进度
            updateEvaluationProgress();
            
            // 启用/禁用提交按钮
            updateSubmitButtonStatus();
          }, 300);
        }
    
        // 检查维度是否完成
        function checkDimensionCompletion(dimensionId) {
          const dimensionData = state.evaluationData[state.selectedStudentId].scores[state.selectedRole][dimensionId];
          if (!dimensionData) return;
          
          const indicators = evaluationDimensions.find(d => d.id === dimensionId).indicators;
          const allScoresFilled = indicators.every(indicator => 
            dimensionData[indicator.id] !== undefined &amp;&amp; dimensionData[indicator.id] !== 0
          );
          
          dimensionData.completed = allScoresFilled;
          saveEvaluationData();
        }
    
        // 更新评价进度
        function updateEvaluationProgress() {
          const roleScores = state.evaluationData[state.selectedStudentId]?.scores[state.selectedRole] || {};
          const completedDimensions = evaluationDimensions.filter(dimension => 
            roleScores[dimension.id]?.completed
          ).length;
          
          document.getElementById(&apos;evaluation-progress&apos;).textContent = `进度：${completedDimensions}/5 维度`;
        }
    
        // 更新提交按钮状态
        function updateSubmitButtonStatus() {
          const roleScores = state.evaluationData[state.selectedStudentId]?.scores[state.selectedRole] || {};
          const allDimensionsCompleted = evaluationDimensions.every(dimension => 
            roleScores[dimension.id]?.completed
          );
          
          document.getElementById(&apos;submit-evaluation-btn&apos;).disabled = !allDimensionsCompleted;
        }
    
        // 提交评价
        function submitEvaluation() {
          if (!state.selectedRole || !state.selectedStudentId) return;
          
          // 更新评价状态
          state.evaluationData[state.selectedStudentId].status[state.selectedRole] = &apos;已完成&apos;;
          saveEvaluationData();
          
          // 显示成功消息
          alert(&apos;评价提交成功！&apos;);
          
          // 重置当前维度
          state.currentDimension = 0;
          
          // 前往成绩页面
          goToPage4();
        }
    
        // 初始化页面4
        function initPage4() {
          if (!state.selectedStudentId) return;
          
          const student = students.find(s => s.id === state.selectedStudentId);
          if (!student) return;
          
          // 更新页面标题
          document.getElementById(&apos;student-score-title&apos;).textContent = `学生成绩 - ${student.name}`;
          document.getElementById(&apos;student-id&apos;).textContent = `学号：${student.id}`;
          
          // 计算并显示总分
          calculateAndDisplayTotalScore();
          
          // 渲染详细评分表格
          renderDetailScoresTable();
          
          // 渲染图表
          renderCharts();
        }
    
        // 计算并显示总分
        function calculateAndDisplayTotalScore() {
          const studentData = state.evaluationData[state.selectedStudentId];
          if (!studentData) return;
          
          // 计算各维度在各身份下的平均分
          const dimensionAverages = {};
          evaluationDimensions.forEach(dimension => {
            // 学生评分
            const studentScores = studentData.scores.student[dimension.id];
            const studentAvg = calculateDimensionAverage(studentScores);
            
            // 教师评分
            const teacherScores = studentData.scores.teacher[dimension.id];
            const teacherAvg = calculateDimensionAverage(teacherScores);
            
            // 企业评分
            const enterpriseScores = studentData.scores.enterprise[dimension.id];
            const enterpriseAvg = calculateDimensionAverage(enterpriseScores);
            
            // 计算维度平均分 (学生×10% + 教师×50% + 企业×40%)
            const avg = (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4);
            dimensionAverages[dimension.id] = avg;
          });
          
          // 计算总分 (各维度平均分×20% 的总和)
          let totalScore = 0;
          Object.values(dimensionAverages).forEach(avg => {
            totalScore += avg * 0.2;
          });
          
          // 显示总分
          document.getElementById(&apos;total-score&apos;).textContent = totalScore.toFixed(1);
        }
    
        // 计算维度平均分
        function calculateDimensionAverage(scoreData) {
          if (!scoreData) return 0;
          
          const scores = Object.values(scoreData).filter(key => key !== &apos;completed&apos;);
          if (scores.length === 0) return 0;
          
          return scores.reduce((sum, score) => sum + score, 0) / scores.length;
        }
    
        // 渲染详细评分表格
        function renderDetailScoresTable() {
          const container = document.getElementById(&apos;detail-scores-table&apos;);
          container.innerHTML = &apos;&apos;;
          
          const studentData = state.evaluationData[state.selectedStudentId];
          if (!studentData) return;
          
          evaluationDimensions.forEach(dimension => {
            const row = document.createElement(&apos;tr&apos;);
            row.className = &apos;border-b hover:bg-gray-50 transition-colors&apos;;
            
            // 学生评分
            const studentScores = studentData.scores.student[dimension.id];
            const studentAvg = calculateDimensionAverage(studentScores);
            
            // 教师评分
            const teacherScores = studentData.scores.teacher[dimension.id];
            const teacherAvg = calculateDimensionAverage(teacherScores);
            
            // 企业评分
            const enterpriseScores = studentData.scores.enterprise[dimension.id];
            const enterpriseAvg = calculateDimensionAverage(enterpriseScores);
            
            // 维度平均分
            const dimensionAvg = (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4);
            
            row.innerHTML = `
              <td class="py-3 px-4 font-medium">${dimension.name}</td>
              <td class="py-3 px-4">${studentAvg.toFixed(1)}</td>
              <td class="py-3 px-4">${teacherAvg.toFixed(1)}</td>
              <td class="py-3 px-4">${enterpriseAvg.toFixed(1)}</td>
              <td class="py-3 px-4 font-semibold">${dimensionAvg.toFixed(1)}</td>
            `;
            
            container.appendChild(row);
          });
        }
    
        // 渲染图表
        function renderCharts() {
          const studentData = state.evaluationData[state.selectedStudentId];
          if (!studentData) return;
          
          // 准备数据
          const dimensionNames = evaluationDimensions.map(d => d.name);
          
          // 各维度在各身份下的平均分
          const studentDimensionAverages = evaluationDimensions.map(d => 
            calculateDimensionAverage(studentData.scores.student[d.id])
          );
          const teacherDimensionAverages = evaluationDimensions.map(d => 
            calculateDimensionAverage(studentData.scores.teacher[d.id])
          );
          const enterpriseDimensionAverages = evaluationDimensions.map(d => 
            calculateDimensionAverage(studentData.scores.enterprise[d.id])
          );
          
          // 各维度的最终平均分
          const dimensionFinalAverages = evaluationDimensions.map(d => {
            const studentAvg = calculateDimensionAverage(studentData.scores.student[d.id]);
            const teacherAvg = calculateDimensionAverage(studentData.scores.teacher[d.id]);
            const enterpriseAvg = calculateDimensionAverage(studentData.scores.enterprise[d.id]);
            return (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4);
          });
          
          // 雷达图
          const radarCtx = document.getElementById(&apos;radar-chart&apos;).getContext(&apos;2d&apos;);
          new Chart(radarCtx, {
            type: &apos;radar&apos;,
            data: {
              labels: dimensionNames,
              datasets: [
                {
                  label: &apos;学生评分&apos;,
                  data: studentDimensionAverages,
                  backgroundColor: &apos;rgba(54, 162, 235, 0.2)&apos;,
                  borderColor: &apos;rgba(54, 162, 235, 1)&apos;,
                  pointBackgroundColor: &apos;rgba(54, 162, 235, 1)&apos;,
                  pointBorderColor: &apos;#fff&apos;,
                  pointHoverBackgroundColor: &apos;#fff&apos;,
                  pointHoverBorderColor: &apos;rgba(54, 162, 235, 1)&apos;
                },
                {
                  label: &apos;教师评分&apos;,
                  data: teacherDimensionAverages,
                  backgroundColor: &apos;rgba(75, 192, 192, 0.2)&apos;,
                  borderColor: &apos;rgba(75, 192, 192, 1)&apos;,
                  pointBackgroundColor: &apos;rgba(75, 192, 192, 1)&apos;,
                  pointBorderColor: &apos;#fff&apos;,
                  pointHoverBackgroundColor: &apos;#fff&apos;,
                  pointHoverBorderColor: &apos;rgba(75, 192, 192, 1)&apos;
                },
                {
                  label: &apos;企业评分&apos;,
                  data: enterpriseDimensionAverages,
                  backgroundColor: &apos;rgba(255, 99, 132, 0.2)&apos;,
                  borderColor: &apos;rgba(255, 99, 132, 1)&apos;,
                  pointBackgroundColor: &apos;rgba(255, 99, 132, 1)&apos;,
                  pointBorderColor: &apos;#fff&apos;,
                  pointHoverBackgroundColor: &apos;#fff&apos;,
                  pointHoverBorderColor: &apos;rgba(255, 99, 132, 1)&apos;
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                r: {
                  beginAtZero: true,
                  max: 100,
                  ticks: {
                    stepSize: 20
                  }
                }
              }
            }
          });
          
          // 柱状图
          const barCtx = document.getElementById(&apos;bar-chart&apos;).getContext(&apos;2d&apos;);
          new Chart(barCtx, {
            type: &apos;bar&apos;,
            data: {
              labels: dimensionNames,
              datasets: [{
                label: &apos;维度平均分&apos;,
                data: dimensionFinalAverages,
                backgroundColor: [
                  &apos;rgba(54, 162, 235, 0.7)&apos;,
                  &apos;rgba(75, 192, 192, 0.7)&apos;,
                  &apos;rgba(153, 102, 255, 0.7)&apos;,
                  &apos;rgba(255, 159, 64, 0.7)&apos;,
                  &apos;rgba(255, 99, 132, 0.7)&apos;
                ],
                borderColor: [
                  &apos;rgba(54, 162, 235, 1)&apos;,
                  &apos;rgba(75, 192, 192, 1)&apos;,
                  &apos;rgba(153, 102, 255, 1)&apos;,
                  &apos;rgba(255, 159, 64, 1)&apos;,
                  &apos;rgba(255, 99, 132, 1)&apos;
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  max: 100,
                  ticks: {
                    stepSize: 20
                  }
                }
              }
            }
          });
          
          // 折线图
          const lineCtx = document.getElementById(&apos;line-chart&apos;).getContext(&apos;2d&apos;);
          new Chart(lineCtx, {
            type: &apos;line&apos;,
            data: {
              labels: dimensionNames,
              datasets: [
                {
                  label: &apos;学生评分&apos;,
                  data: studentDimensionAverages,
                  borderColor: &apos;rgba(54, 162, 235, 1)&apos;,
                  backgroundColor: &apos;rgba(54, 162, 235, 0.1)&apos;,
                  tension: 0.3,
                  fill: true
                },
                {
                  label: &apos;教师评分&apos;,
                  data: teacherDimensionAverages,
                  borderColor: &apos;rgba(75, 192, 192, 1)&apos;,
                  backgroundColor: &apos;rgba(75, 192, 192, 0.1)&apos;,
                  tension: 0.3,
                  fill: true
                },
                {
                  label: &apos;企业评分&apos;,
                  data: enterpriseDimensionAverages,
                  borderColor: &apos;rgba(255, 99, 132, 1)&apos;,
                  backgroundColor: &apos;rgba(255, 99, 132, 0.1)&apos;,
                  tension: 0.3,
                  fill: true
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  max: 100,
                  ticks: {
                    stepSize: 20
                  }
                }
              }
            }
          });
          
          // 饼图
          const pieCtx = document.getElementById(&apos;pie-chart&apos;).getContext(&apos;2d&apos;);
          new Chart(pieCtx, {
            type: &apos;pie&apos;,
            data: {
              labels: [&apos;学生评价 (10%)&apos;, &apos;教师评价 (50%)&apos;, &apos;企业评价 (40%)&apos;],
              datasets: [{
                data: [10, 50, 40],
                backgroundColor: [
                  &apos;rgba(54, 162, 235, 0.7)&apos;,
                  &apos;rgba(75, 192, 192, 0.7)&apos;,
                  &apos;rgba(255, 99, 132, 0.7)&apos;
                ],
                borderColor: [
                  &apos;rgba(54, 162, 235, 1)&apos;,
                  &apos;rgba(75, 192, 192, 1)&apos;,
                  &apos;rgba(255, 99, 132, 1)&apos;
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false
            }
          });
        }
    
        // 导出到Excel
        function exportToExcel() {
          // 创建工作表
          const ws = XLSX.utils.json_to_sheet(createExportData());
          
          // 创建工作簿并添加工作表
          const wb = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(wb, ws, "学生评价数据");
          
          // 导出Excel文件
          XLSX.writeFile(wb, "学生评价数据.xlsx");
        }
    
        // 创建导出数据
        function createExportData() {
          const exportData = [];
          
          // 添加表头
          const headers = [
            "学号", "姓名", "维度", 
            "学生评分", "教师评分", "企业评分", 
            "维度平均分", "评价身份"
          ];
          exportData.push(headers);
          
          // 为每个学生添加数据
          students.forEach(student => {
            const studentData = state.evaluationData[student.id];
            
            // 为每个维度添加数据
            evaluationDimensions.forEach(dimension => {
              // 学生评分
              const studentScores = studentData.scores.student[dimension.id];
              const studentAvg = calculateDimensionAverage(studentScores);
              
              // 教师评分
              const teacherScores = studentData.scores.teacher[dimension.id];
              const teacherAvg = calculateDimensionAverage(teacherScores);
              
              // 企业评分
              const enterpriseScores = studentData.scores.enterprise[dimension.id];
              const enterpriseAvg = calculateDimensionAverage(enterpriseScores);
              
              // 维度平均分
              const dimensionAvg = (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4);
              
              // 添加一行数据
              exportData.push([
                student.id, 
                student.name, 
                dimension.name, 
                studentAvg.toFixed(1),
                teacherAvg.toFixed(1),
                enterpriseAvg.toFixed(1),
                dimensionAvg.toFixed(1),
                "综合"
              ]);
            });
          });
          
          return exportData;
        }
    
        // 初始化应用
        document.addEventListener(&apos;DOMContentLoaded&apos;, initApp);
      </script>
    
        </body></html>
        <div id="root"></div>
      </body>
    </html>
    '
      sandbox="allow-scripts allow-same-origin allow-downloads allow-popups allow-top-navigation-by-user-activation allow-top-navigation-to-custom-protocols"
    ></iframe>
  </body>
</html>
