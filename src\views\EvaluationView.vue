<template>
  <div class="max-w-4xl mx-auto">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center gap-4">
        <a-button @click="goBack" type="default" size="small">
          <i class="fa fa-arrow-left mr-1"></i>
          返回
        </a-button>
        <h2 class="text-2xl font-bold text-primary !mb-0">评价打分</h2>
      </div>
      <div class="text-sm bg-gray-100 rounded-full px-4 py-1">
        进度：{{ evaluationProgress.completed }}/{{ evaluationProgress.total }} 题
      </div>
    </div>

    <!-- 学生信息和服务信息 -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold">
            {{ getRoleText() }}
          </h3>
          <p class="text-sm text-gray-600 !m-0 mt-1">
            服务项目：{{ evaluationStore.selectedService?.dictLabel }}
          </p>
        </div>
        <div class="flex items-center text-sm text-gray-500">
          <i class="fa fa-save mr-1"></i>
          <span :class="saveStatusClass">{{ saveStatus }}</span>
        </div>
      </div>
    </div>

    <!-- 题目导航 -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
      <h4 class="text-md font-semibold mb-3">题目导航</h4>
      <div class="grid grid-cols-5 md:grid-cols-10 gap-2">
        <button
          v-for="(indicator, index) in evaluationStore.indicators"
          :key="indicator.id"
          @click="setCurrentIndicator(index)"
          :class="[
            'w-8 h-8 rounded text-sm font-medium transition-all !m-auto',
            currentIndicatorIndex === index
              ? 'bg-primary !text-white'
              : isIndicatorCompleted(indicator)
                ? 'bg-green-500 !text-white'
                : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
          ]"
        >
          {{ index + 1 }}
        </button>
      </div>
    </div>

    <!-- 当前题目 -->
    <div v-if="currentIndicator" class="bg-white rounded-lg shadow p-6 mb-6">
      <div class="mb-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm text-gray-500">
            第 {{ currentIndicatorIndex + 1 }} 题 / 共 {{ evaluationStore.indicators.length }} 题
          </span>
          <a-tag :color="getDimensionColor(currentIndicator.dimensionality)">
            {{ currentIndicator.dimensionality }}
          </a-tag>
        </div>
        <h3 class="text-lg font-semibold mb-4">
          {{ currentIndicator.evaluatingIndicator }}
        </h3>
      </div>

      <!-- 选择题选项 -->
      <div class="space-y-3">
        <a-radio-group
          v-model:value="currentAnswer"
          class="w-full !flex flex-col gap-3"
          @change="handleAnswerChange"
        >
          <div
            v-for="(option, index) in ratingOptions"
            :key="option.value"
            class="border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <a-radio :value="option.value" class="w-full !p-4">
              <div class="flex items-start">
                <span class="font-semibold text-base mr-3">{{ option.value }}:</span>
                <div class="flex-1">
                  <p class="text-gray-800 text-base !m-0">{{ option.text }}</p>
                </div>
              </div>
            </a-radio>
          </div>
        </a-radio-group>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between mt-6">
        <a-button
          @click="previousIndicator"
          :disabled="currentIndicatorIndex === 0"
          :loading="submitting"
        >
          <i class="fa fa-arrow-left mr-1"></i>
          上一题
        </a-button>

        <div class="space-x-2 flex gap-2">
          <a-button
            type="primary"
            @click="submitCurrentAnswer"
            :disabled="!currentAnswer"
            :loading="submitting"
          >
            <i class="fa fa-save mr-1"></i>
            {{ isIndicatorCompleted(currentIndicator) ? '更新答案' : '保存答案' }}
          </a-button>

          <a-button
            @click="nextIndicator"
            :disabled="currentIndicatorIndex === evaluationStore.indicators.length - 1"
            :loading="submitting"
            type="primary"
            ghost
          >
            下一题
            <i class="fa fa-arrow-right ml-1"></i>
          </a-button>
        </div>
      </div>

      <!-- 提示信息 -->
      <div class="mt-4 text-center text-sm text-gray-500">
        <i class="fa fa-info-circle mr-1"></i>
        点击"下一题"会自动保存当前答案
      </div>
    </div>

    <!-- 完成提示 -->
    <div v-if="evaluationProgress.completed === evaluationProgress.total" class="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
      <i class="fa fa-check-circle text-green-500 text-3xl mb-2"></i>
      <h3 class="text-lg font-semibold text-green-800 mb-2">评价完成！</h3>
      <p class="text-green-700 mb-4">您已完成所有题目的评价。</p>
      <a-button type="primary" @click="goToResults">
        查看结果
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useEvaluationStore } from '@/stores/evaluation'
import { useAuthStore } from '@/stores/auth'
import { message } from 'ant-design-vue'
import {
  Tag as ATag,
  RadioGroup as ARadioGroup,
  Radio as ARadio,
  Button as AButton,
} from 'ant-design-vue'

const route = useRoute()
const router = useRouter()
const evaluationStore = useEvaluationStore()
const authStore = useAuthStore()

const saveStatus = ref('已保存')
const submitting = ref(false)
const currentAnswer = ref('')

const saveStatusClass = computed(() => ({
  'text-green-600': saveStatus.value === '已保存',
  'text-yellow-600': saveStatus.value === '保存中...',
  'text-red-600': saveStatus.value === '保存失败'
}))

const selectedStudent = computed(() => evaluationStore.selectedStudent)
const selectedRole = computed(() => evaluationStore.selectedRole)
const evaluationProgress = computed(() => evaluationStore.evaluationProgress)
const currentIndicatorIndex = computed(() => evaluationStore.currentIndicatorIndex)
const currentIndicator = computed(() => {
  const indicators = evaluationStore.indicators
  if (indicators.length === 0 || currentIndicatorIndex.value >= indicators.length) {
    return null
  }
  return indicators[currentIndicatorIndex.value]
})

// 解析评分标准为选项
const ratingOptions = computed(() => {
  if (!currentIndicator.value?.ratingCriteria) return []

  const criteria = currentIndicator.value.ratingCriteria
  const lines = criteria.split('\n').filter(line => line.trim())

  return lines.map(line => {
    const match = line.match(/^([A-E])：(.+)$/)
    if (match) {
      return {
        value: match[1],
        text: match[2]
      }
    }
    return null
  }).filter(Boolean)
})

/**
 * 获取角色文本
 */
const getRoleText = () => {
  const roleValue = selectedRole.value?.value
  switch (roleValue) {
    case 'student': return '学生评价'
    case 'teacher': return '教师评价'
    case 'enterprise': return '企业评价'
    default: return '未知角色'
  }
}

/**
 * 检查指标是否已完成
 */
const isIndicatorCompleted = (indicator) => {
  return evaluationStore.isIndicatorCompleted(indicator)
}

/**
 * 获取维度颜色
 */
const getDimensionColor = (dimensionality) => {
  const colors = {
    '管理力': 'blue',
    '执行力': 'green',
    '观察力': 'orange',
    '沟通力': 'purple',
    '应变力': 'red'
  }
  return colors[dimensionality] || 'default'
}

/**
 * 设置当前指标
 */
const setCurrentIndicator = async (index) => {
  // 如果当前有答案，先自动提交
  if (currentAnswer.value && currentIndicator.value && index !== currentIndicatorIndex.value) {
    await submitCurrentAnswer(false) // 不显示提交成功消息
  }

  evaluationStore.setCurrentIndicatorIndex(index)
  loadCurrentAnswer()
}

/**
 * 加载当前题目的已保存答案
 */
const loadCurrentAnswer = () => {
  if (!currentIndicator.value) {
    currentAnswer.value = ''
    return
  }

  const existingMark = evaluationStore.getIndicatorMark(currentIndicator.value)
  currentAnswer.value = existingMark?.ratingCriteriaMark || ''
}

/**
 * 处理答案变化
 */
const handleAnswerChange = () => {
  // 答案变化时的处理逻辑
}

/**
 * 提交当前答案
 */
const submitCurrentAnswer = async (showMessage = true) => {
  if (!currentAnswer.value || !currentIndicator.value) {
    if (showMessage) {
      message.warning('请选择一个答案')
    }
    return false
  }

  submitting.value = true
  saveStatus.value = '保存中...'

  try {
    await evaluationStore.submitIndicatorMark(currentIndicator.value, currentAnswer.value)
    saveStatus.value = '已保存'
    if (showMessage) {
      message.success('答案提交成功')
    }
    return true
  } catch (error) {
    saveStatus.value = '保存失败'
    if (showMessage) {
      message.error('提交失败，请重试')
    }
    console.error('Failed to submit answer:', error)
    return false
  } finally {
    submitting.value = false
  }
}

/**
 * 上一题
 */
const previousIndicator = async () => {
  // 如果当前有答案，先自动提交
  if (currentAnswer.value && currentIndicator.value) {
    await submitCurrentAnswer(false) // 不显示提交成功消息
  }

  if (currentIndicatorIndex.value > 0) {
    setCurrentIndicator(currentIndicatorIndex.value - 1)
  }
}

/**
 * 下一题
 */
const nextIndicator = async () => {
  // 如果当前有答案，先自动提交
  if (currentAnswer.value && currentIndicator.value) {
    const success = await submitCurrentAnswer(false) // 不显示提交成功消息
    if (!success) {
      message.error('保存失败，请重试')
      return
    }
  }

  if (currentIndicatorIndex.value < evaluationStore.indicators.length - 1) {
    setCurrentIndicator(currentIndicatorIndex.value + 1)
  }
}

/**
 * 查看结果
 */
const goToResults = () => {
  const studentId = route.params.studentId
  if (studentId) {
    router.push(`/score/${studentId}`)
  } else {
    router.push('/student-selection')
  }
}

/**
 * 返回角色选择界面
 */
const goBack = () => {
  router.push('/')
}

// 组件挂载时的初始化
onMounted(async () => {
  const studentId = route.params.studentId

  if (!authStore.isAuthenticated) {
    message.warning('请先登录')
    router.push('/')
    return
  }

  if (!selectedRole.value?.value) {
    message.warning('请先选择评价身份')
    router.push('/')
    return
  }

  if (!evaluationStore.selectedService) {
    message.warning('请先选择服务项目')
    router.push('/')
    return
  }

  // 指标数据应该在选择服务时已经获取，如果没有则重新获取
  if (evaluationStore.indicators.length === 0) {
    await evaluationStore.fetchIndicators()
  }

  // 如果没有选择学生，则选择当前学生
  if (!selectedStudent.value || selectedStudent.value.id !== studentId) {
    // 检查是否是学生自己评价
    if (authStore.userInfo?.deptId === 201 && authStore.userInfo?.userName === studentId) {
      // 学生自己评价，添加自己的信息到学生列表
      const studentInfo = {
        id: authStore.userInfo.userName,
        name: authStore.userInfo.nickName
      }
      await evaluationStore.selectStudent(studentId, studentInfo)
    } else {
      // 其他情况，需要先获取学生列表
      await evaluationStore.fetchStudents()
      await evaluationStore.selectStudent(studentId)
    }
  }

  if (!selectedStudent.value) {
    message.error('未找到指定学生')
    router.push('/student-selection')
    return
  }

  // 获取学生的成绩数据
  await evaluationStore.fetchMarks()

  // 加载当前题目的答案
  loadCurrentAnswer()
})

// 监听当前指标变化，重新加载答案
watch(
  () => currentIndicatorIndex.value,
  () => {
    loadCurrentAnswer()
  }
)

// 监听学生变化，重新获取成绩
watch(
  () => selectedStudent.value?.id,
  async (newStudentId) => {
    if (newStudentId) {
      await evaluationStore.fetchMarks()
      loadCurrentAnswer()
    }
  }
)
</script>

<style scoped>
.text-primary {
  color: #0f3460;
}

.bg-primary {
  background-color: #0f3460;
}
</style>