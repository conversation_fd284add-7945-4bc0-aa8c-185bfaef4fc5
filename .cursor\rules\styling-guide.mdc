---
description: 
globs: 
alwaysApply: false
---
# 样式规范和UI指南

## 色彩系统
项目使用自定义的色彩系统，在 [tailwind.config.ts](mdc:tailwind.config.ts) 中定义：

```typescript
colors: {
  primary: '#0f3460',    // 深蓝色 - 主要按钮、标题
  secondary: '#1a508b',  // 中蓝色 - 悬停状态
  accent: '#3282b8',     // 浅蓝色 - 强调元素
  light: '#eef2f5',      // 浅灰色 - 次要按钮背景
  success: '#27ae60',    // 绿色 - 成功状态
  warning: '#f39c12',    // 橙色 - 警告状态
  danger: '#e74c3c',     // 红色 - 错误状态
}
```

## 自定义CSS类
在 [src/assets/styles/tailwind.css](mdc:src/assets/styles/tailwind.css) 中定义了项目专用的CSS类：

### 按钮样式
- `.btn-primary` - 主要按钮样式（深蓝色背景）
- `.btn-secondary` - 次要按钮样式（浅色背景）

### 交互效果
- `.card-hover` - 卡片悬停效果（阴影和位移）
- `.airplane-animation` - 飞机图标动画效果
- `.panel-active` - 激活面板样式（蓝色边框和背景）

### 评分相关
- `.score-tooltip` - 评分滑块的提示框样式
- `.slider-container` - 评分滑块容器

## 响应式设计
- 使用 Tailwind CSS 的响应式前缀：`sm:`, `md:`, `lg:`, `xl:`
- 主要断点：
  - 手机端：< 768px
  - 平板端：768px - 1024px  
  - 桌面端：> 1024px

## 组件设计原则
1. **简洁性** - 界面简洁明了，突出核心功能
2. **一致性** - 按钮、卡片、表格等元素保持统一风格
3. **可访问性** - 适当的颜色对比度和字体大小
4. **响应式** - 适配不同屏幕尺寸

## 图标使用
项目使用 Font Awesome 图标库：
- 飞机图标：`fa-plane` (页头动画)
- 用户类型图标：`fa-graduation-cap`, `fa-book`, `fa-building`
- 功能图标：`fa-search`, `fa-save`, `fa-check`, `fa-arrow-left`等

## 布局规范
- 页面最大宽度：`max-w-6xl` (成绩页面), `max-w-4xl` (评价页面)
- 内边距：容器使用 `px-4 py-8`
- 间距：组件间使用 `mb-6`, 小元素间使用 `mb-4`
- 圆角：卡片使用 `rounded-lg`, 按钮使用 `rounded-md`

