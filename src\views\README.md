# 页面组件模块 (Views)

该目录包含应用的所有页面级组件，实现了完整的航空评价系统用户界面和交互流程。

## 📁 文件结构

```
src/views/
├── RoleSelectionView.vue      # 身份选择页面
├── StudentSelectionView.vue   # 学生选择页面
├── EvaluationView.vue         # 评价打分页面
├── ScoreView.vue              # 成绩展示页面
└── README.md                  # 页面组件文档
```

## 🔄 页面流程

### 标准评价流程
```
身份选择 → 学生选择 → 评价打分 → 成绩展示
    ↓         ↓         ↓         ↓
   首页    学生列表    五维评价    图表展示
```

### 查看成绩流程
```
身份选择 → 学生选择 → 成绩展示
    ↓         ↓         ↓
   首页    学生列表    图表展示
```

## 📄 页面详解

### `RoleSelectionView.vue` - 身份选择页面

**功能**: 应用首页，用户选择评价身份的入口页面

#### 主要特性
- **身份选择**: 支持学生、教师、企业三种身份
- **权重显示**: 显示各身份在总分中的占比
- **认证检查**: 未登录用户显示提示信息
- **智能导航**: 根据用户类型自动跳转到相应页面

#### 核心功能
```javascript
const selectRole = (role) => {
  evaluationStore.selectRole(role)
  
  const userDeptId = authStore.userInfo?.deptId
  if (userDeptId === 201) {
    // 学生用户，直接评价自己
    const studentId = authStore.userInfo?.userName
    router.push(`/evaluation/${studentId}`)
  } else {
    // 教师或企业用户，进入学生选择列表
    router.push('/student-selection')
  }
}
```

#### 界面元素
- **身份卡片**: 三个身份选择卡片，显示图标、名称和权重
- **查看成绩按钮**: 快速进入成绩查看模式
- **登录状态提示**: 未登录时显示警告信息

#### 用户体验
- **悬停效果**: 卡片悬停时有阴影和位移动画
- **禁用状态**: 未登录时按钮和卡片呈禁用状态
- **响应式设计**: 移动端自适应布局

### `StudentSelectionView.vue` - 学生选择页面

**功能**: 显示学生列表，支持选择学生进行评价或查看成绩

#### 主要特性
- **学生列表**: 表格形式展示学生信息
- **评价状态**: 显示每个学生的评价完成状态
- **分页功能**: 支持大量学生数据的分页显示
- **双重模式**: 支持评价模式和查看成绩模式

#### 核心功能
```javascript
const handleStudentClick = (student) => {
  if (isViewingScores.value) {
    // 查看成绩模式
    evaluationStore.selectStudent(student.id)
    router.push('/score')
  } else {
    // 评价模式
    router.push(`/evaluation/${student.id}`)
  }
}
```

#### 界面元素
- **学生表格**: 显示学号、姓名、评价状态
- **状态标识**: 不同颜色标识评价完成状态
- **分页控件**: 上一页/下一页按钮
- **返回按钮**: 返回身份选择页面

#### 数据管理
- **动态加载**: 页面挂载时自动获取学生列表
- **状态计算**: 实时计算学生评价状态
- **分页处理**: 支持服务端分页

### `EvaluationView.vue` - 评价打分页面

**功能**: 核心评价界面，实现五维度评价打分功能

#### 主要特性
- **五维度评价**: 管理力、执行力、观察力、沟通力、应变力
- **滑块评分**: 每个指标使用滑块进行0-100分评价
- **实时保存**: 评分数据实时保存到后端
- **进度显示**: 显示评价完成进度
- **权限控制**: 学生只能评价自己

#### 核心功能
```javascript
const updateScore = async (dimensionName, indicatorName, event) => {
  const value = parseInt(event.target.value)
  
  saveStatus.value = '保存中...'
  await evaluationStore.addOrUpdateMark(dimensionName, indicatorName, value)
  
  setTimeout(() => {
    saveStatus.value = '已保存'
  }, 300)
}
```

#### 界面元素
- **学生信息**: 显示被评价学生的基本信息
- **维度卡片**: 每个维度独立的评价卡片
- **评分滑块**: 直观的滑块式评分控件
- **评分标准**: 详细的评分标准说明
- **保存状态**: 实时显示数据保存状态

#### 数据处理
- **权限验证**: 检查用户是否有权限评价指定学生
- **数据同步**: 评分数据实时同步到后端
- **状态管理**: 管理评价进度和完成状态

### `ScoreView.vue` - 成绩展示页面

**功能**: 综合展示学生评价结果，提供多种图表可视化

#### 主要特性
- **综合评分**: 显示学生最终综合得分
- **多种图表**: 雷达图、柱状图、折线图、饼图
- **数据导出**: 支持Excel格式数据导出
- **详细分析**: 各维度详细评分分析

#### 图表类型
1. **综合评分圆环**: 显示最终总分
2. **评价占比饼图**: 显示三种身份评价的权重占比
3. **五力雷达图**: 五个维度的雷达图展示
4. **维度柱状图**: 各维度平均分对比
5. **趋势折线图**: 各身份评分趋势对比

#### 核心功能
```javascript
const exportToExcel = () => {
  const wb = XLSX.utils.book_new()
  const wsData = prepareExcelData()
  const ws = XLSX.utils.aoa_to_sheet(wsData)
  
  XLSX.utils.book_append_sheet(wb, ws, '评价结果')
  XLSX.writeFile(wb, `${selectedStudent.value?.name}_评价结果.xlsx`)
}
```

#### 数据可视化
- **Chart.js集成**: 使用Chart.js创建各种图表
- **响应式图表**: 图表自适应容器大小
- **交互功能**: 图表支持悬停和点击交互
- **颜色主题**: 统一的颜色主题设计

## 🚀 使用指南

### 1. 页面导航

```javascript
// 编程式导航到各个页面
import { useRouter } from 'vue-router'

const router = useRouter()

// 跳转到身份选择页面
router.push('/')

// 跳转到学生选择页面
router.push('/student-selection')

// 跳转到评价页面（带学生ID参数）
router.push(`/evaluation/${studentId}`)

// 跳转到成绩展示页面
router.push('/score')
```

### 2. 状态管理集成

```javascript
// 在页面组件中使用Store
import { useEvaluationStore } from '@/stores/evaluation'
import { useAuthStore } from '@/stores/auth'

const evaluationStore = useEvaluationStore()
const authStore = useAuthStore()

// 选择身份
evaluationStore.selectRole('teacher')

// 选择学生
evaluationStore.selectStudent(studentId)

// 设置查看模式
evaluationStore.setViewingScores(true)
```

### 3. 权限控制

```javascript
// 检查用户权限
const checkPermission = () => {
  if (!authStore.isAuthenticated) {
    message.warning('请先登录')
    return false
  }
  
  const userDeptId = authStore.userInfo?.deptId
  if (userDeptId === 201 && userInfo.userName !== studentId) {
    message.error('您只能对自己进行评价')
    return false
  }
  
  return true
}
```

### 4. 数据处理

```javascript
// 计算综合评分
const calculateTotalScore = () => {
  let totalScore = 0
  
  evaluationDimensions.forEach(dimension => {
    const studentAvg = calculateDimensionAverage(studentScores[dimension.id])
    const teacherAvg = calculateDimensionAverage(teacherScores[dimension.id])
    const enterpriseAvg = calculateDimensionAverage(enterpriseScores[dimension.id])
    
    const dimensionScore = (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4)
    totalScore += dimensionScore * 0.2 // 每个维度占20%
  })
  
  return totalScore
}
```

## 🎨 样式设计

### 1. 设计系统
- **主色调**: 深蓝色 (#0f3460)
- **辅助色**: 绿色、橙色、红色用于状态标识
- **字体**: 系统字体栈，支持中英文
- **间距**: 基于Tailwind CSS的间距系统

### 2. 响应式设计
```css
/* 移动端适配 */
.grid-cols-1 md:grid-cols-2 lg:grid-cols-3

/* 文字大小适配 */
.text-xl md:text-2xl lg:text-3xl

/* 间距适配 */
.p-4 md:p-6 lg:p-8
```

### 3. 交互效果
```css
/* 悬停效果 */
.card-hover:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-0.25rem);
}

/* 按钮状态 */
.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
```

## 🔧 开发指南

### 1. 页面组件结构
```vue
<template>
  <!-- 页面内容 -->
</template>

<script setup>
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 2. 状态管理
const router = useRouter()
const store = useStore()

// 3. 响应式数据
const loading = ref(false)

// 4. 计算属性
const computedValue = computed(() => {
  // 计算逻辑
})

// 5. 方法定义
const handleAction = () => {
  // 处理逻辑
}

// 6. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

### 2. 错误处理
```javascript
// 统一错误处理
const handleError = (error, defaultMessage = '操作失败') => {
  console.error(error)
  message.error(error.message || defaultMessage)
}

// 在异步操作中使用
try {
  await someAsyncOperation()
} catch (error) {
  handleError(error, '加载数据失败')
}
```

### 3. 加载状态管理
```javascript
const loading = ref(false)

const loadData = async () => {
  loading.value = true
  try {
    const data = await fetchData()
    // 处理数据
  } catch (error) {
    handleError(error)
  } finally {
    loading.value = false
  }
}
```

## 🔗 相关文件

- [`@/stores/evaluation.js`](../stores/evaluation.js) - 评价状态管理
- [`@/stores/auth.js`](../stores/auth.js) - 认证状态管理
- [`@/constants/data.js`](../constants/data.js) - 评价维度数据
- [`@/router/index.js`](../router/index.js) - 路由配置
- [`@/api/`](../api/) - API接口调用

## 📝 开发规范

### 1. 命名规范
- **页面组件**: 使用PascalCase + View后缀
- **方法名**: 使用camelCase，动词开头
- **变量名**: 使用camelCase，语义化命名

### 2. 代码组织
- **逻辑分离**: 将复杂逻辑提取到composables
- **组件复用**: 提取可复用的UI组件
- **状态管理**: 合理使用Store管理状态

### 3. 性能优化
- **懒加载**: 大型组件使用动态导入
- **计算属性**: 使用computed缓存计算结果
- **事件处理**: 避免在模板中使用内联函数

### 4. 用户体验
- **加载状态**: 提供明确的加载反馈
- **错误处理**: 友好的错误提示信息
- **响应式**: 确保移动端良好体验
- **无障碍**: 提供适当的aria属性
