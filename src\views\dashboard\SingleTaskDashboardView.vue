<template>
  <!-- 全屏背景层 -->
  <div class="dashboard-background"></div>

  <div class="dashboard-container" :class="{ fullscreen: isFullscreen }">
    <!-- 大屏头部 -->
    <div class="dashboard-header">
      <div class="dashboard-header-left">
        <a-button
          type="primary"
          ghost
          @click="goBack"
          class="back-button !flex justify-center items-center"
        >
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回首页
        </a-button>
        <h1 class="dashboard-title">{{ serviceTitle }} - 单任务分析大屏</h1>
      </div>
      <div class="dashboard-controls">
        <a-select
          v-model:value="selectedService"
          placeholder="选择服务任务"
          style="width: 200px"
          @change="onServiceChange"
        >
          <a-select-option
            v-for="service in services"
            :key="service.dictValue"
            :value="service.dictValue"
          >
            {{ service.dictLabel }}
          </a-select-option>
        </a-select>
        <a-button
          type="primary"
          @click="refreshData"
          :loading="loading"
          class="!flex justify-center items-center"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新数据
        </a-button>
        <a-switch
          v-model:checked="autoRefresh"
          @change="toggleAutoRefresh"
          checked-children="自动刷新"
          un-checked-children="手动刷新"
          style="margin-right: 16px"
        />
      </div>
    </div>

    <!-- 数据加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="正在加载数据..." />
    </div>

    <!-- 大屏内容 -->
    <div v-else class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-number">{{ totalStudents }}</div>
          <div class="stat-label">参评学生</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ averageScore.toFixed(1) }}</div>
          <div class="stat-label">平均总分</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ completionRate }}%</div>
          <div class="stat-label">评价完成率</div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-grid">
        <!-- 学生雷达图区域 -->
        <div class="chart-section">
          <h3 class="chart-title">学生能力雷达图</h3>
          <div class="student-radar-container">
            <div class="student-selector">
              <a-select
                v-model:value="selectedStudentId"
                placeholder="选择学生"
                style="width: 200px"
                @change="updateStudentRadar"
              >
                <a-select-option
                  v-for="student in students"
                  :key="student.userName"
                  :value="student.userName"
                >
                  {{ student.nickName }}
                </a-select-option>
              </a-select>
            </div>
            <div class="radar-chart-wrapper">
              <v-chart
                ref="studentRadarChart"
                :option="studentRadarOption"
                class="chart"
              />
            </div>
          </div>
        </div>

        <!-- 班级排名表格 -->
        <div class="chart-section">
          <h3 class="chart-title">班级排名</h3>
          <div class="ranking-table-wrapper">
            <a-table
              :columns="rankingColumns"
              :data-source="rankingData"
              :pagination="false"
              size="small"
              :scroll="{ y: 'calc(100vh - 300px)' }"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'rank'">
                  <span class="rank-badge" :class="getRankClass(index + 1)">
                    {{ index + 1 }}
                  </span>
                </template>
                <template v-else-if="column.key === 'totalScore'">
                  <span class="score-text">{{
                    convertScoreToGrade(record.totalScore)
                  }}</span>
                </template>
                <template v-else-if="['management', 'execution', 'observation', 'communication', 'adaptability'].includes(column.key)">
                  <span class="grade-text">{{
                    convertScoreToGrade(record[column.key])
                  }}</span>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons-vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { RadarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import { useAuthStore } from '@/stores/auth'
import { listMarks } from '@/api/marks'
import { getServiceList } from '@/api/service'
import { listUser } from '@/api/user'

// 注册ECharts组件
use([
  CanvasRenderer,
  RadarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
])

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const services = ref([])
const selectedService = ref('')
const students = ref([])
const selectedStudentId = ref('')
const marksData = ref([])
const isFullscreen = ref(false)
const autoRefresh = ref(true)
const refreshInterval = ref(30) // 30秒自动刷新
const refreshTimer = ref(null)
const scale = ref(1)
const resizeTimer = ref(null) // 新增：用于防抖

// 计算缩放比例
const calculateScale = () => {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  // 目标16:9比例
  const targetRatio = 16 / 9
  const currentRatio = windowWidth / windowHeight

  let scaleX, scaleY

  if (currentRatio > targetRatio) {
    // 屏幕更宽，以高度为准
    scaleY = windowHeight / 1080
    scaleX = (windowHeight * targetRatio) / 1920
  } else {
    // 屏幕更高，以宽度为准
    scaleX = windowWidth / 1920
    scaleY = windowWidth / targetRatio / 1080
  }

  // 取较小的缩放比例，确保内容完全显示
  const scale = Math.min(scaleX, scaleY, 1) // 最大不超过1，避免放大

  // 设置最小缩放比例，确保内容可读
  const minScale = 0.3
  return Math.max(scale, minScale)
}

// 更新缩放
const updateScale = () => {
  const newScale = calculateScale()
  scale.value = newScale

  const container = document.querySelector('.dashboard-container')
  if (container) {
    // 计算居中偏移
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const containerWidth = 1920 * newScale
    const containerHeight = 1080 * newScale

    const offsetX = Math.max(0, (windowWidth - containerWidth) / 2)
    const offsetY = Math.max(0, (windowHeight - containerHeight) / 2)

    container.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${newScale})`
    container.style.transformOrigin = 'top left'
  }
}

// 监听窗口大小变化
const handleResize = () => {
  // 添加防抖，避免频繁调用
  clearTimeout(resizeTimer.value)
  resizeTimer.value = setTimeout(() => {
    updateScale()
  }, 100)
}

// 计算属性
const serviceTitle = computed(() => {
  const service = services.value.find(
    (s) => s.dictValue === selectedService.value,
  )
  return service ? service.dictLabel : '未选择服务'
})

const totalStudents = computed(() => students.value.length)

const averageScore = computed(() => {
  if (rankingData.value.length === 0) return 0
  const total = rankingData.value.reduce(
    (sum, item) => sum + item.totalScore,
    0,
  )
  return total / rankingData.value.length
})

const completionRate = computed(() => {
  if (students.value.length === 0) return 0
  const completedCount = students.value.filter((student) => {
    const studentMarks = marksData.value.filter(
      (mark) => mark.num === student.num,
    )
    return studentMarks.length > 0
  }).length
  return Math.round((completedCount / students.value.length) * 100)
})

// 五力维度
const dimensions = ['管理力', '执行力', '观察力', '沟通力', '应变力']

// 学生雷达图配置
const studentRadarOption = ref({
  tooltip: {
    trigger: 'item',
  },
  radar: {
    indicator: dimensions.map((dim) => ({
      name: dim,
      max: 100,
      nameTextStyle: {
        color: '#ffffff',
        fontSize: 12,
      },
    })),
    splitArea: {
      areaStyle: {
        color: ['rgba(114, 172, 209, 0.2)', 'rgba(114, 172, 209, 0.4)'],
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
      },
    },
  },
  series: [
    {
      type: 'radar',
      data: [],
    },
  ],
})

// 分数转等级
const convertScoreToGrade = (score) => {
  if (score >= 80) return 'A'
  if (score >= 60) return 'B'
  if (score >= 40) return 'C'
  if (score >= 20) return 'D'
  return 'E'
}

// 排名表格列配置
const rankingColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    align: 'center',
  },
  {
    title: '学号',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
  },
  {
    title: '姓名',
    dataIndex: 'nickName',
    key: 'nickName',
    width: 100,
  },
  {
    title: '总分',
    key: 'totalScore',
    width: 80,
    align: 'center',
  },
  {
    title: '管理力',
    key: 'management',
    width: 80,
    align: 'center',
  },
  {
    title: '执行力',
    key: 'execution',
    width: 80,
    align: 'center',
  },
  {
    title: '观察力',
    key: 'observation',
    width: 80,
    align: 'center',
  },
  {
    title: '沟通力',
    key: 'communication',
    width: 80,
    align: 'center',
  },
  {
    title: '应变力',
    key: 'adaptability',
    width: 80,
    align: 'center',
  },
]

// 排名数据
const rankingData = computed(() => {
  if (!selectedService.value || students.value.length === 0) return []

  const studentScores = students.value.map((student) => {
    const studentMarks = marksData.value.filter(
      (mark) =>
        mark.num === student.userName && mark.service === selectedService.value,
    )

    const scores = calculateStudentScores(studentMarks)
    return {
      userName: student.userName,
      nickName: student.nickName,
      ...scores,
      totalScore: scores.totalScore,
    }
  })

  return studentScores.sort((a, b) => b.totalScore - a.totalScore)
})

// 方法
const fetchServices = async () => {
  try {
    const response = await getServiceList()
    services.value = response.data || []
    console.log('服务列表加载成功:', services.value.length, '个服务')

    // 如果路由参数中有服务，则设置为默认选择
    if (route.params.service) {
      selectedService.value = route.params.service
    } else if (services.value.length > 0) {
      selectedService.value = services.value[0].dictValue
    }
  } catch (error) {
    console.error('获取服务列表失败:', error)
    message.error('获取服务列表失败')
    services.value = [] // 确保有默认值
    throw error // 重新抛出错误，让调用者知道失败了
  }
}

const fetchStudents = async () => {
  try {
    const response = await listUser({
      pageNum: 1,
      pageSize: 100,
      deptId: 201, // 学生部门ID
    })
    students.value = response.rows || []
    console.log('学生列表加载成功:', students.value.length, '个学生')

    if (students.value.length > 0) {
      selectedStudentId.value =
        students.value[0].userName || students.value[0].num
    }
  } catch (error) {
    console.error('获取学生列表失败:', error)
    message.error('获取学生列表失败')
    students.value = [] // 确保有默认值
    throw error // 重新抛出错误，让调用者知道失败了
  }
}

const fetchMarksData = async () => {
  if (!selectedService.value) {
    console.warn('未选择服务，跳过获取评分数据')
    return
  }

  try {
    const response = await listMarks({
      pageNum: 1,
      pageSize: 1000,
      service: selectedService.value,
    })
    marksData.value = response.rows || []
    console.log('评分数据加载成功:', marksData.value.length, '条记录')
  } catch (error) {
    console.error('获取评分数据失败:', error)
    message.error('获取评分数据失败')
    marksData.value = [] // 确保有默认值
    throw error // 重新抛出错误，让调用者知道失败了
  }
}

// 等级转分数映射
const convertGradeToScore = (grade) => {
  const gradeMap = {
    A: 100,
    B: 80,
    C: 60,
    D: 40,
    E: 20,
  }
  return gradeMap[grade] || 0
}

const calculateStudentScores = (studentMarks) => {
  const dimensionScores = {}
  let totalScore = 0

  dimensions.forEach((dimension) => {
    const dimensionMarks = studentMarks.filter(
      (mark) => mark.dimensionality === dimension,
    )

    let studentScore = 0,
      teacherScore = 0,
      enterpriseScore = 0

    dimensionMarks.forEach((mark) => {
      const score = convertGradeToScore(mark.ratingCriteriaMark)
      switch (mark.identity) {
        case '学生':
          studentScore = score
          break
        case '教师':
          teacherScore = score
          break
        case '企业':
          enterpriseScore = score
          break
      }
    })

    // 按权重计算维度得分：学生10% + 教师50% + 企业40%
    const dimensionScore =
      studentScore * 0.1 + teacherScore * 0.5 + enterpriseScore * 0.4

    // 根据维度名称映射到正确的字段
    const dimensionKey = {
      '管理力': 'management',
      '执行力': 'execution',
      '观察力': 'observation',
      '沟通力': 'communication',
      '应变力': 'adaptability'
    }[dimension]

    if (dimensionKey) {
      dimensionScores[dimensionKey] = dimensionScore
    }

    // 每个维度占总分的20%
    totalScore += dimensionScore * 0.2
  })

  return {
    management: dimensionScores.management || 0,
    execution: dimensionScores.execution || 0,
    observation: dimensionScores.observation || 0,
    communication: dimensionScores.communication || 0,
    adaptability: dimensionScores.adaptability || 0,
    totalScore,
  }
}

const updateStudentRadar = () => {
  if (!selectedStudentId.value || !selectedService.value) return

  const studentMarks = marksData.value.filter(
    (mark) =>
      mark.num === selectedStudentId.value &&
      mark.service === selectedService.value,
  )

  const scores = calculateStudentScores(studentMarks)
  const radarData = [
    parseFloat(scores.management),
    parseFloat(scores.execution),
    parseFloat(scores.observation),
    parseFloat(scores.communication),
    parseFloat(scores.adaptability),
  ]

  const student = students.value.find(
    (s) => s.userName === selectedStudentId.value,
  )
  const studentName = student ? student.nickName : '未知学生'

  studentRadarOption.value.series[0].data = [
    {
      value: radarData,
      name: studentName,
      itemStyle: {
        color: '#00d4ff',
      },
      areaStyle: {
        color: 'rgba(0, 212, 255, 0.3)',
      },
    },
  ]
}

const onServiceChange = () => {
  refreshData()
}

const refreshData = async () => {
  loading.value = true
  try {
    await fetchMarksData()

    // 确保数据加载完成后再更新图表
    await nextTick()
    updateStudentRadar()

    console.log('数据刷新完成')
  } catch (error) {
    console.error('刷新数据失败:', error)
    message.error('数据刷新失败，请重试')
  } finally {
    loading.value = false
  }
}

const getRankClass = (rank) => {
  if (rank === 1) return 'rank-gold'
  if (rank === 2) return 'rank-silver'
  if (rank === 3) return 'rank-bronze'
  return 'rank-normal'
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement
      .requestFullscreen()
      .then(() => {
        isFullscreen.value = true
        updateScale()
      })
      .catch((err) => {
        console.error('进入全屏失败:', err)
        message.error('进入全屏失败')
      })
  } else {
    document
      .exitFullscreen()
      .then(() => {
        isFullscreen.value = false
        updateScale()
      })
      .catch((err) => {
        console.error('退出全屏失败:', err)
        message.error('退出全屏失败')
      })
  }
}

const startAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }

  if (autoRefresh.value) {
    refreshTimer.value = setInterval(() => {
      refreshData()
    }, refreshInterval.value * 1000)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

const toggleAutoRefresh = (checked) => {
  if (checked) {
    startAutoRefresh()
    message.success(`已开启自动刷新，每${refreshInterval.value}秒更新一次`)
  } else {
    stopAutoRefresh()
    message.info('已关闭自动刷新')
  }
}

const goBack = () => {
  router.push('/')
}

// 监听选中学生变化
watch(selectedStudentId, updateStudentRadar)

// 组件挂载
onMounted(async () => {
  loading.value = true
  try {
    // 确保用户已登录且有权限
    if (!authStore.isAuthenticated) {
      console.warn('用户未登录，跳转到首页')
      window.location.href = '/'
      return
    }

    // 如果用户信息不存在，先获取用户信息
    if (!authStore.userInfo) {
      try {
        await authStore.getProfile()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        window.location.href = '/'
        return
      }
    }

    // 检查用户权限
    const userDeptId = authStore.userInfo?.deptId
    const allowedRoles = [103, 202, 203] // 管理员、教师和企业
    if (!allowedRoles.includes(userDeptId)) {
      console.warn('用户权限不足，无法访问此页面')
      window.location.href = '/'
      return
    }

    // 从路由参数获取服务ID
    const serviceId = route.params.service
    if (serviceId) {
      selectedService.value = serviceId
    }

    // 按顺序加载数据，避免并发问题
    await fetchServices()
    await fetchStudents()

    // 如果没有从路由获取到服务ID，使用第一个服务
    if (!selectedService.value && services.value.length > 0) {
      selectedService.value = services.value[0].dictValue
    }

    await refreshData()

    // 启动自动刷新
    if (autoRefresh.value) {
      startAutoRefresh()
    }

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)

    // 初始化缩放
    await nextTick()
    updateScale()
  } catch (error) {
    console.error('页面初始化失败:', error)
    message.error('页面加载失败，请刷新重试')
  } finally {
    loading.value = false
  }
})

// 组件卸载
onUnmounted(() => {
  stopAutoRefresh()
  window.removeEventListener('resize', handleResize)
  if (resizeTimer.value) {
    clearTimeout(resizeTimer.value)
  }
})
</script>

<style scoped>
/* 全屏背景层 */
.dashboard-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1e3f 0%, #1a365d 50%, #2d5a87 100%);
  z-index: 999;
}

.dashboard-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(
      circle at 20% 80%,
      rgba(0, 212, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(0, 212, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(0, 212, 255, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.dashboard-container {
  width: 1920px;
  height: 1080px;
  background: transparent;
  color: #ffffff;
  padding: 16px;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  transform-origin: top left;
  transition: transform 0.3s ease;
  box-sizing: border-box; /* 确保padding包含在尺寸内 */
}

.dashboard-container.fullscreen {
  width: 100vw;
  height: 100vh;
  padding: 20px; /* 全屏时增加内边距 */
}

.dashboard-container > * {
  position: relative;
  z-index: 1;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 16px;
  height: 80px;
  flex-shrink: 0;
  min-height: 80px; /* 确保最小高度 */
}

.dashboard-header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  border: 1px solid rgba(0, 212, 255, 0.5) !important;
  color: #00d4ff !important;
  background: rgba(0, 212, 255, 0.1) !important;
  transition: all 0.3s ease;
}

.back-button:hover {
  border-color: #00d4ff !important;
  background: rgba(0, 212, 255, 0.2) !important;
  color: #ffffff !important;
  transform: translateX(-5px);
}

.dashboard-title {
  font-size: 32px;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(45deg, #00d4ff, #0099cc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.dashboard-content {
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  padding-bottom: 20px; /* 添加底部安全边距 */
  min-height: 0; /* 允许容器收缩 */
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  color: #00d4ff;
  margin-bottom: 8px;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.stat-label {
  font-size: 16px;
  color: #b3d9ff;
  font-weight: 500;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  flex: 1;
  min-height: 600px; /* 设置明确的最小高度 */
  height: calc(100vh - 200px); /* 设置固定高度 */
  overflow: visible; /* 允许内容正常显示 */
}

.chart-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  overflow: hidden; /* 防止内容溢出 */
  display: flex;
  flex-direction: column;
  min-height: 500px; /* 设置最小高度 */
}

.chart-title {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 16px 0;
  text-align: center;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  flex-shrink: 0; /* 防止标题被压缩 */
}

.student-radar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许容器收缩 */
}

.student-selector {
  margin-bottom: 16px;
  text-align: center;
  flex-shrink: 0; /* 防止选择器被压缩 */
}

.radar-chart-wrapper,
.ranking-table-wrapper {
  flex: 1;
  min-height: 500px; /* 设置更大的最小高度 */
  height: calc(100vh - 250px); /* 设置固定高度，减去头部和统计卡片的高度 */
  overflow: hidden;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 排名表格样式 */
.rank-badge {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  font-size: 12px;
}

.rank-badge.rank-1 {
  background: #ffd700;
  color: #000;
}

.rank-badge.rank-2 {
  background: #c0c0c0;
  color: #000;
}

.rank-badge.rank-3 {
  background: #cd7f32;
  color: #fff;
}

.rank-badge.rank-other {
  background: rgba(0, 212, 255, 0.3);
  color: #fff;
}

.score-text {
  color: #00d4ff;
  font-weight: bold;
}

.grade-text {
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 4px;
  color: #ffffff;
  background-color: #00d4ff;
}

/* 自定义选择器样式 */
:deep(.ant-select-selector) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  color: #ffffff !important;
}

:deep(.ant-select-selection-item) {
  color: #ffffff !important;
}

:deep(.ant-select-arrow) {
  color: #00d4ff !important;
}

:deep(.ant-table) {
  background: transparent !important;
  color: #ffffff !important;
}

:deep(.ant-table-thead > tr > th) {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
}

:deep(.ant-table-tbody > tr > td) {
  background: transparent !important;
  color: #ffffff !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: rgba(0, 212, 255, 0.1) !important;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-title {
    font-size: 28px;
  }

  .stats-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .stat-number {
    font-size: 28px;
  }
}

@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    gap: 24px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .stats-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .dashboard-title {
    font-size: 24px;
  }

  .dashboard-controls {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
