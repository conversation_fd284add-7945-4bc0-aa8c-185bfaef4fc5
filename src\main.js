import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue'
import ECharts from 'vue-echarts'

import 'ant-design-vue/dist/reset.css'
import './assets/styles/main.scss'
import './assets/styles/tailwind.css'
import '@fortawesome/fontawesome-free/css/all.min.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd)
app.component('VChart', ECharts)

app.mount('#app')
