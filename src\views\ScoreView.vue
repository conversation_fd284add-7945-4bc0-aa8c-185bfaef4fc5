<template>
  <div class="max-w-6xl mx-auto">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-2xl font-bold text-primary">
          <span v-if="isStudent">我的成绩</span>
          <span v-else>学生成绩 - {{ selectedStudent?.name || '未选择学生' }}</span>
        </h2>
        <div class="flex items-center space-x-4 text-gray-600">
          <span>学号：{{ selectedStudent?.id || '无' }}</span>
          <span v-if="isTeacherOrEnterprise" class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
            {{ userDeptId === 202 ? '教师查看' : '企业查看' }}
          </span>
        </div>
      </div>
      <div class="flex space-x-3">
        <button @click="exportToExcel" class="btn-primary" :disabled="!selectedStudent">
          <i class="fa fa-file-excel-o mr-1"></i> 导出表格
        </button>
        <button v-if="isTeacherOrEnterprise" @click="toggleComparisonMode" class="btn-secondary">
          <i class="fa fa-chart-line mr-1"></i>
          {{ showComparison ? '隐藏对比' : '显示对比' }}
        </button>
      </div>
    </div>

    <!-- 服务选择 -->
    <div v-if="selectedStudent" class="bg-white rounded-lg shadow p-4 mb-6">
      <!-- 学生版本：单服务选择 -->
      <div v-if="isStudent" class="flex items-center space-x-4">
        <label class="text-sm font-medium text-gray-700">选择服务项目：</label>
        <select
          v-model="currentSelectedService"
          @change="onServiceChange"
          class="border border-gray-300 rounded-md px-3 py-2 bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">请选择服务项目</option>
          <option
            v-for="service in availableServices"
            :key="service.dictValue"
            :value="service.dictValue"
          >
            {{ service.dictLabel }}
          </option>
        </select>
        <span v-if="currentSelectedService" class="text-sm text-gray-500">
          当前服务：{{ getServiceLabel(currentSelectedService) }}
        </span>
      </div>

      <!-- 教师/企业版本：多服务选择和对比 -->
      <div v-else-if="isTeacherOrEnterprise" class="space-y-4">
        <div class="flex items-center space-x-4">
          <label class="text-sm font-medium text-gray-700">主要服务项目：</label>
          <select
            v-model="currentSelectedService"
            @change="onServiceChange"
            class="border border-gray-300 rounded-md px-3 py-2 bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">请选择主要服务项目</option>
            <option
              v-for="service in availableServices"
              :key="service.dictValue"
              :value="service.dictValue"
            >
              {{ service.dictLabel }}
            </option>
          </select>
        </div>

        <div class="flex items-center space-x-4">
          <label class="text-sm font-medium text-gray-700">对比服务项目：</label>
          <div class="flex flex-wrap gap-2">
            <label
              v-for="service in availableServices"
              :key="service.dictValue"
              class="flex items-center space-x-1 text-sm"
            >
              <input
                type="checkbox"
                :value="service.dictValue"
                v-model="selectedServicesForComparison"
                @change="onComparisonServicesChange"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span>{{ service.dictLabel }}</span>
            </label>
          </div>
        </div>

        <div v-if="selectedServicesForComparison.length > 0" class="text-sm text-gray-600">
          <span class="font-medium">已选择对比服务：</span>
          {{ selectedServicesForComparison.map(s => getServiceLabel(s)).join('、') }}
        </div>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="!selectedStudent" class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center mb-6">
      <i class="fa fa-exclamation-triangle text-yellow-500 text-3xl mb-2"></i>
      <h3 class="text-lg font-semibold text-yellow-800 mb-2">未找到学生信息</h3>
      <p class="text-yellow-700 mb-4">请先选择一个学生查看成绩。</p>
      <button @click="goBack" class="btn-primary">
        返回选择学生
      </button>
    </div>

    <!-- 主要内容 - 只有当有学生数据时才显示 -->
    <div v-if="selectedStudent">
    <!-- 五力综合评分 -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <h3 class="text-xl font-semibold mb-6 text-center">五力综合评分</h3>

      <!-- 总分显示 -->
      <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-32 h-32 rounded-full bg-gradient-to-r from-blue-500 to-green-500 text-white">
          <div>
            <div class="text-4xl font-bold">{{ totalGrade }}</div>
            <div class="text-sm opacity-90">总评</div>
          </div>
        </div>
      </div>

      <!-- 五力维度得分 -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div
          v-for="dimension in evaluationDimensions"
          :key="dimension.id"
          class="bg-gray-50 rounded-lg p-4 text-center"
        >
          <div class="text-3xl font-bold text-primary mb-2">
            {{ getFinalDimensionGrade(dimension.id) }}
          </div>
          <div class="text-sm font-medium text-gray-700 mb-1">{{ dimension.name }}</div>
          <div class="text-xs text-gray-500">
            综合得分
          </div>
        </div>
      </div>

      <!-- 评价占比说明 -->
      <div class="mt-6 bg-blue-50 rounded-lg p-4">
        <h4 class="text-sm font-semibold text-gray-700 mb-2">评价权重说明</h4>
        <div class="flex flex-wrap gap-4 text-sm text-gray-600">
          <span class="flex items-center">
            <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
            学生评价 10%
          </span>
          <span class="flex items-center">
            <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
            教师评价 50%
          </span>
          <span class="flex items-center">
            <div class="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
            企业评价 40%
          </span>
        </div>
      </div>
    </div>
    
    <!-- 图表展示 -->
    <div v-if="isStudent" class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <!-- 学生版本：简化图表 -->
      <!-- 五力雷达图 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">五力能力雷达图</h3>
        <div class="h-64">
          <canvas ref="radarChartRef"></canvas>
        </div>
        <p class="text-sm text-gray-600 mt-2 text-center">
          展示您在五个维度的综合能力分布
        </p>
      </div>

      <!-- 各维度得分柱状图 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">各维度得分对比</h3>
        <div class="h-64">
          <canvas ref="barChartRef"></canvas>
        </div>
        <p class="text-sm text-gray-600 mt-2 text-center">
          当前服务项目下的各维度最终得分
        </p>
      </div>
    </div>

    <!-- 教师/企业版本：完整图表 -->
    <div v-else-if="isTeacherOrEnterprise" class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <!-- 五力雷达图 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">五力能力雷达图</h3>
        <div class="h-64">
          <canvas ref="radarChartRef"></canvas>
        </div>
        <p class="text-sm text-gray-600 mt-2 text-center">
          学生在五个维度的综合能力分布
        </p>
      </div>

      <!-- 各维度得分柱状图 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">各维度得分对比</h3>
        <div class="h-64">
          <canvas ref="barChartRef"></canvas>
        </div>
        <p class="text-sm text-gray-600 mt-2 text-center">
          主要服务项目下的各维度最终得分
        </p>
      </div>

      <!-- 多服务趋势对比图 -->
      <div class="bg-white rounded-lg shadow p-6 md:col-span-2">
        <h3 class="text-lg font-semibold mb-4">多服务成绩趋势对比</h3>
        <div class="h-80">
          <canvas ref="lineChartRef"></canvas>
        </div>
        <p class="text-sm text-gray-600 mt-2 text-center">
          不同服务项目下的成绩变化趋势和评价身份对比
        </p>
      </div>

      <!-- 评价身份对比图 -->
      <div class="bg-white rounded-lg shadow p-6 md:col-span-2">
        <h3 class="text-lg font-semibold mb-4">评价身份对比分析</h3>
        <div class="h-64">
          <canvas ref="roleComparisonChartRef"></canvas>
        </div>
        <p class="text-sm text-gray-600 mt-2 text-center">
          学生、教师、企业三方评价的差异分析
        </p>
      </div>
    </div>
    
    <!-- 评分详情 -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-semibold">评分详情</h3>
        <div v-if="isTeacherOrEnterprise" class="text-sm text-gray-600">
          <span class="mr-4">当前服务：{{ getServiceLabel(currentSelectedService) }}</span>
          <span v-if="selectedServicesForComparison.length > 0">
            对比服务：{{ selectedServicesForComparison.length }}个
          </span>
        </div>
      </div>

      <!-- 维度详情卡片 -->
      <div class="space-y-4">
        <div
          v-for="dimension in evaluationDimensions"
          :key="dimension.id"
          class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex justify-between items-center mb-3">
            <h4 class="text-lg font-semibold text-gray-800">{{ dimension.name }}</h4>
            <div class="text-right">
              <div class="text-3xl font-bold text-primary">{{ getFinalDimensionGrade(dimension.id) }}</div>
              <div class="text-sm text-gray-500">综合等级</div>
            </div>
          </div>

          <!-- 各身份评分 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 rounded-lg p-3">
              <div class="text-sm text-gray-600 mb-1">学生评分 (10%)</div>
              <div class="text-xl font-semibold text-blue-600">
                {{ getDimensionGrade('student', dimension.id) }}
              </div>
              <div class="text-xs text-gray-500">
                平均分: {{ getDimensionScore('student', dimension.id).toFixed(1) }}
              </div>
            </div>
            <div class="bg-green-50 rounded-lg p-3">
              <div class="text-sm text-gray-600 mb-1">教师评分 (50%)</div>
              <div class="text-xl font-semibold text-green-600">
                {{ getDimensionGrade('teacher', dimension.id) }}
              </div>
              <div class="text-xs text-gray-500">
                平均分: {{ getDimensionScore('teacher', dimension.id).toFixed(1) }}
              </div>
            </div>
            <div class="bg-red-50 rounded-lg p-3">
              <div class="text-sm text-gray-600 mb-1">企业评分 (40%)</div>
              <div class="text-xl font-semibold text-red-600">
                {{ getDimensionGrade('enterprise', dimension.id) }}
              </div>
              <div class="text-xs text-gray-500">
                平均分: {{ getDimensionScore('enterprise', dimension.id).toFixed(1) }}
              </div>
            </div>
          </div>

          <!-- 进度条显示 -->
          <div class="mt-3">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>得分进度</span>
              <span>{{ getFinalDimensionScore(dimension.id).toFixed(1) }}/100</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-500"
                :style="{ width: `${getFinalDimensionScore(dimension.id)}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

    <!-- 底部按钮 -->
    <div class="flex justify-between">
      <button @click="goBack" class="btn-secondary">
        <i class="fa fa-arrow-left mr-1"></i> 返回
      </button>
      <button @click="goHome" class="btn-secondary">
        <i class="fa fa-home mr-1"></i> 返回首页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useEvaluationStore } from '@/stores/evaluation'
import { useAuthStore } from '@/stores/auth'
import { evaluationDimensions } from '@/constants/data'
import { Chart, registerables } from 'chart.js'
import * as XLSX from 'xlsx'
import { message } from 'ant-design-vue'

/**
 * @typedef {import('@/types').Role} Role
 */

// 注册Chart.js组件
Chart.register(...registerables)

const router = useRouter()
const route = useRoute()
const evaluationStore = useEvaluationStore()
const authStore = useAuthStore()

// 图表引用
/** @type {import('vue').Ref<HTMLCanvasElement | undefined>} */
const radarChartRef = ref()
/** @type {import('vue').Ref<HTMLCanvasElement | undefined>} */
const barChartRef = ref()
/** @type {import('vue').Ref<HTMLCanvasElement | undefined>} */
const lineChartRef = ref()
/** @type {import('vue').Ref<HTMLCanvasElement | undefined>} */
const roleComparisonChartRef = ref()

// 图表实例
/** @type {Chart | null} */
let radarChart = null
/** @type {Chart | null} */
let barChart = null
/** @type {Chart | null} */
let lineChart = null
/** @type {Chart | null} */
let roleComparisonChart = null

const selectedStudent = computed(() => evaluationStore.selectedStudent)

// 用户角色检测
const userDeptId = computed(() => authStore.userInfo?.deptId)
const isStudent = computed(() => userDeptId.value === 201)
const isTeacherOrEnterprise = computed(() => userDeptId.value === 202 || userDeptId.value === 203)

// 服务选择相关
/** @type {import('vue').Ref<Array>} */
const availableServices = ref([])
/** @type {import('vue').Ref<string>} */
const currentSelectedService = ref('')
/** @type {import('vue').Ref<Array<string>>} */
const selectedServicesForComparison = ref([]) // 教师/企业多服务对比
/** @type {import('vue').Ref<boolean>} */
const showComparison = ref(false) // 是否显示对比模式

// 计算总分（保留数值版本用于兼容）
const totalScore = computed(() => {
  if (!evaluationStore.selectedStudentId || !evaluationStore.selectedStudent || !evaluationStore.selectedService) return 0
  try {
    return evaluationStore.calculateTotalScore() || 0
  } catch (error) {
    console.error('Error calculating total score:', error)
    return 0
  }
})

// 计算总评等级
const totalGrade = computed(() => {
  if (!evaluationStore.selectedStudentId || !evaluationStore.selectedStudent || !evaluationStore.selectedService) return '-'
  try {
    return evaluationStore.calculateTotalGrade() || '-'
  } catch (error) {
    console.error('Error calculating total grade:', error)
    return '-'
  }
})

/**
 * 获取维度分数
 * @param {Role} role - 角色
 * @param {string} dimensionId - 维度ID
 * @returns {number} 维度分数
 */
const getDimensionScore = (role, dimensionId) => {
  if (!evaluationStore.selectedStudent || !evaluationStore.selectedService) return 0
  const dimension = evaluationDimensions.find(d => d.id === dimensionId)
  if (!dimension) return 0
  try {
    return evaluationStore.calculateDimensionAverage(role, dimension.name) || 0
  } catch (error) {
    console.error('Error calculating dimension score:', error)
    return 0
  }
}

/**
 * 获取最终维度分数（加权平均）
 * @param {string} dimensionId - 维度ID
 * @returns {number} 最终维度分数
 */
const getFinalDimensionScore = (dimensionId) => {
  const studentAvg = getDimensionScore('student', dimensionId)
  const teacherAvg = getDimensionScore('teacher', dimensionId)
  const enterpriseAvg = getDimensionScore('enterprise', dimensionId)

  return (studentAvg * 0.1) + (teacherAvg * 0.5) + (enterpriseAvg * 0.4)
}

/**
 * 获取最终维度等级（加权平均后转换为等级）
 * @param {string} dimensionId - 维度ID
 * @returns {string} 最终维度等级
 */
const getFinalDimensionGrade = (dimensionId) => {
  if (!evaluationStore.selectedStudent || !evaluationStore.selectedService) return '-'
  const dimension = evaluationDimensions.find(d => d.id === dimensionId)
  if (!dimension) return '-'
  try {
    return evaluationStore.calculateDimensionGrade(dimension.name) || '-'
  } catch (error) {
    console.error('Error calculating dimension grade:', error)
    return '-'
  }
}

/**
 * 获取维度等级（单个角色）
 * @param {Role} role - 角色
 * @param {string} dimensionId - 维度ID
 * @returns {string} 维度等级
 */
const getDimensionGrade = (role, dimensionId) => {
  const score = getDimensionScore(role, dimensionId)
  return evaluationStore.scoreToGrade(score)
}

// 准备图表数据
const prepareChartData = () => {
  const dimensionNames = evaluationDimensions.map(d => d.name)
  
  const studentScores = evaluationDimensions.map(d => getDimensionScore('student', d.id))
  const teacherScores = evaluationDimensions.map(d => getDimensionScore('teacher', d.id))
  const enterpriseScores = evaluationDimensions.map(d => getDimensionScore('enterprise', d.id))
  const finalScores = evaluationDimensions.map(d => getFinalDimensionScore(d.id))
  
  return {
    dimensionNames,
    studentScores,
    teacherScores,
    enterpriseScores,
    finalScores
  }
}



// 渲染雷达图
const renderRadarChart = () => {
  if (!radarChartRef.value) return

  const ctx = radarChartRef.value.getContext('2d')
  if (!ctx) return

  if (radarChart) {
    radarChart.destroy()
  }

  const chartData = prepareChartData()

  radarChart = new Chart(ctx, {
    type: 'radar',
    data: {
      labels: chartData.dimensionNames,
      datasets: [
        {
          label: '综合得分',
          data: chartData.finalScores,
          backgroundColor: 'rgba(59, 130, 246, 0.3)',
          borderColor: 'rgba(59, 130, 246, 1)',
          pointBackgroundColor: 'rgba(59, 130, 246, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(59, 130, 246, 1)',
          pointRadius: 6,
          pointHoverRadius: 8,
          borderWidth: 3
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        r: {
          beginAtZero: true,
          max: 100,
          ticks: {
            stepSize: 20,
            font: {
              size: 12
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          angleLines: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        }
      }
    }
  })
}

// 渲染柱状图
const renderBarChart = () => {
  if (!barChartRef.value) return
  
  const ctx = barChartRef.value.getContext('2d')
  if (!ctx) return
  
  if (barChart) {
    barChart.destroy()
  }
  
  const chartData = prepareChartData()
  
  barChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: chartData.dimensionNames,
      datasets: [{
        label: '维度平均分',
        data: chartData.finalScores,
        backgroundColor: [
          'rgba(54, 162, 235, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(153, 102, 255, 0.7)',
          'rgba(255, 159, 64, 0.7)',
          'rgba(255, 99, 132, 0.7)'
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)',
          'rgba(255, 99, 132, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          ticks: {
            stepSize: 20
          }
        }
      }
    }
  })
}

// 渲染多服务趋势折线图（教师/企业版本）
const renderLineChart = async () => {
  if (!lineChartRef.value || !isTeacherOrEnterprise.value) return

  const ctx = lineChartRef.value.getContext('2d')
  if (!ctx) return

  if (lineChart) {
    lineChart.destroy()
  }

  const comparisonData = await getMultiServiceComparisonData()
  if (!comparisonData) return

  const dimensionNames = evaluationDimensions.map(d => d.name)
  const datasets = []

  // 为每个服务创建数据集
  const colors = [
    'rgba(59, 130, 246, 1)',   // 蓝色
    'rgba(16, 185, 129, 1)',   // 绿色
    'rgba(245, 101, 101, 1)',  // 红色
    'rgba(139, 92, 246, 1)',   // 紫色
    'rgba(245, 158, 11, 1)',   // 橙色
  ]

  let colorIndex = 0
  Object.entries(comparisonData).forEach(([serviceValue, data]) => {
    const color = colors[colorIndex % colors.length]

    datasets.push({
      label: `${data.label} - 最终得分`,
      data: data.dimensionScores.map(d => d.final),
      borderColor: color,
      backgroundColor: color.replace('1)', '0.1)'),
      tension: 0.3,
      fill: false,
      pointRadius: 5,
      pointHoverRadius: 7
    })

    colorIndex++
  })

  lineChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: dimensionNames,
      datasets: datasets
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          ticks: {
            stepSize: 20
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        x: {
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      }
    }
  })
}

// 渲染角色对比图（教师/企业版本）
const renderRoleComparisonChart = () => {
  if (!roleComparisonChartRef.value || !isTeacherOrEnterprise.value) return

  const ctx = roleComparisonChartRef.value.getContext('2d')
  if (!ctx) return

  if (roleComparisonChart) {
    roleComparisonChart.destroy()
  }

  const chartData = prepareChartData()

  roleComparisonChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: chartData.dimensionNames,
      datasets: [
        {
          label: '学生评分',
          data: chartData.studentScores,
          backgroundColor: 'rgba(59, 130, 246, 0.7)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1
        },
        {
          label: '教师评分',
          data: chartData.teacherScores,
          backgroundColor: 'rgba(16, 185, 129, 0.7)',
          borderColor: 'rgba(16, 185, 129, 1)',
          borderWidth: 1
        },
        {
          label: '企业评分',
          data: chartData.enterpriseScores,
          backgroundColor: 'rgba(245, 101, 101, 0.7)',
          borderColor: 'rgba(245, 101, 101, 1)',
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          ticks: {
            stepSize: 20
          }
        }
      }
    }
  })
}

// 渲染所有图表
const renderCharts = async () => {
  await nextTick()
  renderRadarChart()
  renderBarChart()

  if (isTeacherOrEnterprise.value) {
    await renderLineChart()
    renderRoleComparisonChart()
  }
}

/**
 * 获取可用的服务列表
 */
const fetchAvailableServices = async () => {
  try {
    await evaluationStore.fetchServices()
    availableServices.value = evaluationStore.services
    // 如果已经有选择的服务，设置为当前选择
    if (evaluationStore.selectedService) {
      currentSelectedService.value = evaluationStore.selectedService.dictValue
    }
  } catch (error) {
    console.error('Failed to fetch services:', error)
    availableServices.value = []
  }
}

/**
 * 获取服务标签
 * @param {string} serviceValue - 服务值
 * @returns {string} 服务标签
 */
const getServiceLabel = (serviceValue) => {
  const service = availableServices.value.find(s => s.dictValue === serviceValue)
  return service ? service.dictLabel : serviceValue
}

/**
 * 切换对比模式
 */
const toggleComparisonMode = () => {
  showComparison.value = !showComparison.value
  if (showComparison.value && selectedServicesForComparison.value.length === 0) {
    // 如果开启对比模式但没有选择对比服务，自动选择第一个可用服务
    if (availableServices.value.length > 1) {
      const firstOtherService = availableServices.value.find(s => s.dictValue !== currentSelectedService.value)
      if (firstOtherService) {
        selectedServicesForComparison.value = [firstOtherService.dictValue]
      }
    }
  }
  renderCharts()
}

/**
 * 服务选择变化处理
 */
const onServiceChange = async () => {
  if (!currentSelectedService.value) return

  const service = availableServices.value.find(s => s.dictValue === currentSelectedService.value)
  if (service) {
    await evaluationStore.selectService(service)
    // 重新获取成绩数据（查看成绩模式，获取所有人的评价记录）
    await evaluationStore.fetchMarks(false)
    // 重新渲染图表
    renderCharts()
  }
}

/**
 * 对比服务选择变化处理
 */
const onComparisonServicesChange = async () => {
  if (isTeacherOrEnterprise.value) {
    // 重新渲染对比图表
    renderCharts()
  }
}

/**
 * 获取多服务对比数据
 */
const getMultiServiceComparisonData = async () => {
  if (!selectedStudent.value || !isTeacherOrEnterprise.value) return null

  const allServices = [currentSelectedService.value, ...selectedServicesForComparison.value]
    .filter(Boolean)
    .filter((service, index, arr) => arr.indexOf(service) === index) // 去重

  const comparisonData = {}

  for (const serviceValue of allServices) {
    const service = availableServices.value.find(s => s.dictValue === serviceValue)
    if (!service) continue

    // 临时切换服务获取数据
    const originalService = evaluationStore.selectedService
    await evaluationStore.selectService(service)
    await evaluationStore.fetchMarks(false) // 查看成绩模式，获取所有人的评价记录

    // 收集该服务的数据
    comparisonData[serviceValue] = {
      label: service.dictLabel,
      dimensionScores: evaluationDimensions.map(d => ({
        dimension: d.name,
        student: getDimensionScore('student', d.id),
        teacher: getDimensionScore('teacher', d.id),
        enterprise: getDimensionScore('enterprise', d.id),
        final: getFinalDimensionScore(d.id)
      }))
    }

    // 恢复原服务
    if (originalService) {
      await evaluationStore.selectService(originalService)
      await evaluationStore.fetchMarks(false) // 查看成绩模式，获取所有人的评价记录
    }
  }

  return comparisonData
}

/**
 * 导出Excel
 */
const exportToExcel = () => {
  /** @type {any[][]} */
  const exportData = []
  
  // 添加表头
  exportData.push([
    '学号', '姓名', '维度', 
    '学生评分', '教师评分', '企业评分', 
    '维度平均分'
  ])
  
  // 添加数据
  if (selectedStudent.value) {
    evaluationDimensions.forEach(dimension => {
      exportData.push([
        selectedStudent.value.id,
        selectedStudent.value.name,
        dimension.name,
        getDimensionScore('student', dimension.id).toFixed(1),
        getDimensionScore('teacher', dimension.id).toFixed(1),
        getDimensionScore('enterprise', dimension.id).toFixed(1),
        getFinalDimensionScore(dimension.id).toFixed(1)
      ])
    })
  }
  
  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(exportData)
  
  // 创建工作簿
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '学生评价数据')
  
  // 导出文件
  const fileName = `${selectedStudent.value?.name || '学生'}_评价数据.xlsx`
  XLSX.writeFile(wb, fileName)
}

// 返回上一页
const goBack = () => {
  if (evaluationStore.isViewingScores) {
    router.push('/student-selection')
  } else {
    router.push('/')
  }
}

// 返回首页
const goHome = () => {
  router.push('/')
  evaluationStore.resetSelection()
}

// 监听学生变化，重新渲染图表
watch(selectedStudent, (student) => {
  if (!student) {
    router.push('/')
    return
  }
  renderCharts()
}, { immediate: false })

// 组件挂载时的权限检查和数据加载
onMounted(async () => {
  const studentId = route.params.studentId

  // 检查用户是否已登录
  if (!authStore.isAuthenticated) {
    message.warning('请先登录')
    router.push('/')
    return
  }

  // 获取可用服务列表
  await fetchAvailableServices()

  // 权限检查
  const userDeptId = authStore.userInfo?.deptId
  if (userDeptId === 201) {
    // 学生只能查看自己的成绩
    if (authStore.userInfo?.userName !== studentId) {
      message.error('您只能查看自己的成绩')
      router.push('/')
      return
    }
  } else if (userDeptId !== 103 && userDeptId !== 202 && userDeptId !== 203) {
    // 只有管理员(103)、教师(202)、企业(203)可以查看所有学生成绩
    message.error('您没有权限查看成绩')
    router.push('/')
    return
  }

  // 确保有服务选择
  if (!evaluationStore.selectedService) {
    // 尝试从localStorage恢复
    await evaluationStore.initializeFromStorage()
    if (!evaluationStore.selectedService) {
      message.warning('请先选择服务项目')
      router.push('/')
      return
    }
  }

  // 选择学生
  if (userDeptId === 201) {
    // 学生查看自己的成绩
    const studentInfo = {
      id: authStore.userInfo.userName,
      name: authStore.userInfo.nickName
    }
    await evaluationStore.selectStudent(studentId, studentInfo, false)
  } else {
    // 教师/企业/管理员查看指定学生成绩
    await evaluationStore.fetchStudents()
    await evaluationStore.selectStudent(studentId, null, false)
  }

  if (!evaluationStore.selectedStudent) {
    message.error('未找到指定学生')
    router.push('/')
    return
  }

  // 获取成绩数据（查看成绩模式，获取所有人的评价记录）
  await evaluationStore.fetchMarks(false)

  // 渲染图表
  renderCharts()
})

// 组件卸载时销毁图表
const destroyCharts = () => {
  if (radarChart) radarChart.destroy()
  if (barChart) barChart.destroy()
  if (lineChart) lineChart.destroy()
  if (roleComparisonChart) roleComparisonChart.destroy()
}

// 监听路由变化，销毁图表
watch(() => router.currentRoute.value.name, (newRoute) => {
  if (newRoute !== 'Score') {
    destroyCharts()
  }
})
</script>

<style lang="scss" scoped>
.btn-primary {
  background-color: #0f3460;
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.btn-primary:hover {
  background-color: #1a508b;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #eef2f5;
  color: #0f3460;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.text-primary {
  color: #0f3460;
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bg-white {
  animation: fadeInUp 0.5s ease-out;
}

// 进度条动画
.bg-gradient-to-r {
  transition: width 0.8s ease-in-out;
}

// 悬停效果
.hover\:shadow-md:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}
</style>