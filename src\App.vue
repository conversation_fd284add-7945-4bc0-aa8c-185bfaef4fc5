<template>
  <div id="app" class="min-h-screen flex flex-col bg-gray-50 font-sans">
    <!-- 导航栏 -->
    <AppHeader />
    
    <!-- 主内容区 -->
    <main class="flex-grow container mx-auto px-4 py-8">
      <RouterView />
    </main>
    
    <!-- 页脚 -->
    <AppFooter />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useEvaluationStore } from '@/stores/evaluation'
import { useAuthStore } from '@/stores/auth'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'

const evaluationStore = useEvaluationStore()
const authStore = useAuthStore()

// 在 setup 阶段立即检查认证状态并获取用户信息
// 这比 onMounted 更早，可以更快地更新UI和状态
if (authStore.isAuthenticated) {
  authStore.getProfile().then(() => {
    // 获取用户信息成功后，根据角色决定是否获取学生列表
    const userDeptId = authStore.userInfo?.deptId
    if (userDeptId && userDeptId !== 201) {
      // 只有非学生用户（教师、企业）才获取学生列表
      evaluationStore.fetchStudents()
    }
  }).catch(error => {
    // getProfile action中已经处理了登出，这里可以不用额外处理
    console.error('App setup: Authentication check failed.', error)
  })
}

onMounted(() => {
  // onMounted 可以保留用于处理那些确实需要DOM渲染后才能执行的逻辑
  // 目前这里可以留空
})
</script>
