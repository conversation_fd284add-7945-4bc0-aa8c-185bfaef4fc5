<template>
  <a-modal
    :open="props.open"
    :closable="true"
    :mask-closable="false"
    title="登录"
    :footer="null"
    @cancel="handleCancel"
  >
    <a-form
      :model="formState"
      @finish="onFinish"
      @finish-failed="onFinishFailed"
      layout="vertical"
    >
      <a-form-item
        label="用户名"
        name="username"
        :rules="[{ required: true, message: '请输入用户名' }]"
      >
        <a-input v-model:value="formState.username" />
      </a-form-item>

      <a-form-item
        label="密码"
        name="password"
        :rules="[{ required: true, message: '请输入密码' }]"
      >
        <a-input-password v-model:value="formState.password" />
      </a-form-item>

      <a-form-item>
        <a-button type="primary" html-type="submit" :loading="loading" block>
          登录
        </a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { message } from 'ant-design-vue'

/**
 * @typedef {import('@/types').LoginData} LoginData
 */

// Define props
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
})

// Define emits
const emit = defineEmits(['update:open', 'success'])

const authStore = useAuthStore()
/** @type {LoginData} */
const formState = reactive({
  username: '',
  password: '',
})
/** @type {import('vue').Ref<boolean>} */
const loading = ref(false)

const handleCancel = () => {
  emit('update:open', false)
}

/**
 * 处理登录表单提交
 * @param {LoginData} values - 表单数据
 */
const onFinish = async (values) => {
  loading.value = true
  try {
    await authStore.login(values)
    message.success('登录成功')
    emit('success')
    emit('update:open', false) // 登录成功后关闭对话框
  } catch (error) {
    message.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理登录表单提交失败
 * @param {any} errorInfo - 错误信息
 */
const onFinishFailed = (errorInfo) => {
  console.log('Failed:', errorInfo)
}
</script>

<style lang="scss">
[data-slot='dialog-overlay'] {
  backdrop-filter: blur(8px);
}

.blur-mask {
  backdrop-filter: blur(64px) saturate(150%);
  mask: linear-gradient(to top, #fff 0%, transparent 45%);
  pointer-events: none;
  background: var(--background-dark-2);
}
</style>
