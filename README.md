# 客舱设施与服务五力评价系统

这是一个基于 Vue3 + TypeScript 开发的航空职业教育评价平台，用于对学生的客舱设施与服务能力进行多维度评价。

## 功能特性

### 🎯 核心功能
- **多身份评价**: 支持学生、教师、企业三种身份评价，权重分别为 10%、50%、40%
- **五力评价模型**: 管理力、执行力、观察力、沟通力、应变力五个维度
- **实时评分**: 支持滑块式评分，实时保存评价数据
- **数据可视化**: 提供雷达图、柱状图、折线图、饼图等多种图表展示
- **Excel导出**: 支持评价数据导出为Excel文件

### 📊 评价维度
1. **管理力**: 全流程完整性、跨岗位协作、文化资源整合、时效管理
2. **执行力**: 动作标准化、设备使用、时效性、细节处理
3. **观察力**: 显性需求响应、隐性需求洞察、风险预判、文化敏感度
4. **沟通力**: 语言策略、非语言技巧、方言应用、禁忌规避
5. **应变力**: 流程异常处理、冲突化解、极端案例应对、文化冲突调解

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **开发语言**: TypeScript
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **样式框架**: Tailwind CSS
- **图表库**: Chart.js
- **构建工具**: Vite
- **包管理器**: pnpm

## 项目结构

```
src/
├── components/           # 公共组件
│   ├── AppHeader.vue    # 页头组件
│   ├── AppFooter.vue    # 页脚组件
│   └── ui/              # UI组件库
├── views/               # 页面组件
│   ├── RoleSelectionView.vue      # 身份选择页面
│   ├── StudentSelectionView.vue   # 学生选择页面
│   ├── EvaluationView.vue         # 评价打分页面
│   └── ScoreView.vue              # 成绩展示页面
├── stores/              # 状态管理
│   └── evaluation.ts    # 评价数据状态
├── types/               # 类型定义
│   └── index.ts         # 全局类型
├── constants/           # 常量数据
│   └── data.ts          # 学生数据和评价维度
├── assets/              # 静态资源
│   └── styles/          # 样式文件
└── router/              # 路由配置
    └── index.ts
```

## 开发指南

### 环境要求
- Node.js >= 16
- pnpm >= 7

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

### 代码检查
```bash
pnpm lint
```

## 使用流程

1. **选择身份**: 在首页选择评价身份（学生/教师/企业）
2. **选择学生**: 从学生列表中选择要评价的学生
3. **进行评价**: 对五个维度的各项指标进行0-100分评价
4. **查看成绩**: 通过图表和表格查看评价结果
5. **导出数据**: 将评价结果导出为Excel文件

## 数据存储

项目使用浏览器 localStorage 进行数据持久化，评价数据会自动保存到本地存储中。

## 评分规则

- 每个维度包含4个指标，每个指标评分范围0-100分
- 维度平均分 = 该维度所有指标的平均分
- 学生最终成绩 = Σ(维度平均分 × 身份权重) × 维度权重(20%)
- 身份权重：学生10% + 教师50% + 企业40%

## 许可证

本项目仅用于教育目的。

## 更新日志

### v1.0.0 (2025-01-09)
- 完成项目从原始HTML到Vue3+TypeScript的完整转换
- 实现多身份评价功能
- 添加五力评价模型
- 集成Chart.js图表展示
- 支持Excel数据导出
- 完整的响应式设计
