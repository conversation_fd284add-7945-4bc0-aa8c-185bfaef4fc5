# 常量模块 (Constants)

该目录包含项目的所有常量数据定义，为应用提供稳定的配置数据和业务规则。

## 📁 文件结构

```
src/constants/
├── data.js           # 主要常量数据文件
└── README.md         # 常量模块文档
```

## 🎯 设计原则

### 1. 业务数据集中管理
- 将业务相关的常量数据统一存放
- 避免在组件中硬编码业务数据
- 便于维护和修改业务规则

### 2. 数据结构标准化
- 使用一致的数据结构格式
- 提供完整的数据类型定义
- 支持数据验证和类型检查

### 3. 可扩展性设计
- 支持新增评价维度和指标
- 便于调整权重配置
- 易于扩展新的业务规则

## 📄 文件详解

### `data.js` - 主要常量数据

**功能**: 定义航空评价系统的核心业务数据，包括评价维度、指标和权重配置

#### 1. 评价维度数据 (`evaluationDimensions`)

**结构**: 五力评价模型的完整定义

```javascript
export const evaluationDimensions = [
  {
    id: 'management',           // 维度唯一标识
    name: '管理力',             // 维度名称
    indicators: [               // 指标列表
      {
        id: 'm1',              // 指标唯一标识
        name: '全流程完整性',    // 指标名称
        criteria: '评分标准...' // 详细评分标准
      }
      // ... 更多指标
    ]
  }
  // ... 更多维度
]
```

**五个评价维度**:

1. **管理力 (management)**
   - `m1` - 全流程完整性
   - `m2` - 跨岗位协作
   - `m3` - 文化资源整合
   - `m4` - 时效管理

2. **执行力 (execution)**
   - `e1` - 动作标准化
   - `e2` - 设备使用
   - `e3` - 时效性
   - `e4` - 细节处理

3. **观察力 (observation)**
   - `o1` - 显性需求响应
   - `o2` - 隐性需求洞察
   - `o3` - 风险预判
   - `o4` - 文化敏感度

4. **沟通力 (communication)**
   - `c1` - 语言策略
   - `c2` - 非语言技巧
   - `c3` - 方言应用
   - `c4` - 禁忌规避

5. **应变力 (adaptability)**
   - `a1` - 流程异常处理
   - `a2` - 冲突化解
   - `a3` - 极端案例应对
   - `a4` - 文化冲突调解

**评分标准**: 每个指标都有详细的5级评分标准
- **80-100分**: 优秀表现，超出预期
- **61-80分**: 良好表现，符合标准
- **41-60分**: 一般表现，基本合格
- **21-40分**: 较差表现，需要改进
- **0-20分**: 未完成或不合格

#### 2. 身份权重配置 (`roleWeights`)

**结构**: 三种评价身份的权重配置

```javascript
export const roleWeights = [
  {
    role: 'student',           // 身份标识
    name: '学生评价',          // 身份名称
    weight: 10,               // 权重百分比
    icon: 'fa-graduation-cap' // 图标类名
  },
  {
    role: 'teacher',
    name: '教师评价',
    weight: 50,
    icon: 'fa-book'
  },
  {
    role: 'enterprise',
    name: '企业评价',
    weight: 40,
    icon: 'fa-building'
  }
]
```

**权重分配**:
- **学生评价**: 10% - 学生自评或互评
- **教师评价**: 50% - 专业教师评价（主要权重）
- **企业评价**: 40% - 行业专家评价

## 🚀 使用指南

### 1. 导入常量数据

```javascript
import { evaluationDimensions, roleWeights } from '@/constants/data'

// 获取所有评价维度
console.log(evaluationDimensions)

// 获取身份权重配置
console.log(roleWeights)
```

### 2. 在组件中使用

```vue
<script setup>
import { evaluationDimensions, roleWeights } from '@/constants/data'

// 渲染评价维度列表
const dimensions = evaluationDimensions

// 获取特定维度
const managementDimension = evaluationDimensions.find(d => d.id === 'management')

// 渲染身份选择
const roles = roleWeights
</script>

<template>
  <!-- 维度列表 -->
  <div v-for="dimension in dimensions" :key="dimension.id">
    <h3>{{ dimension.name }}</h3>
    <div v-for="indicator in dimension.indicators" :key="indicator.id">
      <span>{{ indicator.name }}</span>
      <p>{{ indicator.criteria }}</p>
    </div>
  </div>
  
  <!-- 身份选择 -->
  <div v-for="role in roles" :key="role.role">
    <i :class="role.icon"></i>
    <span>{{ role.name }} ({{ role.weight }}%)</span>
  </div>
</template>
```

### 3. 在Store中使用

```javascript
import { defineStore } from 'pinia'
import { evaluationDimensions, roleWeights } from '@/constants/data'

export const useEvaluationStore = defineStore('evaluation', () => {
  // 初始化评价数据结构
  const initializeEvaluationData = (studentId) => {
    const data = { scores: {} }
    
    roleWeights.forEach(roleWeight => {
      data.scores[roleWeight.role] = {}
      
      evaluationDimensions.forEach(dimension => {
        data.scores[roleWeight.role][dimension.id] = {}
        
        dimension.indicators.forEach(indicator => {
          data.scores[roleWeight.role][dimension.id][indicator.id] = 0
        })
      })
    })
    
    return data
  }
  
  return { initializeEvaluationData }
})
```

### 4. 计算评分

```javascript
import { evaluationDimensions, roleWeights } from '@/constants/data'

// 计算维度平均分
const calculateDimensionAverage = (dimensionScores) => {
  const scores = Object.values(dimensionScores).filter(score => typeof score === 'number')
  return scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0
}

// 计算学生最终成绩
const calculateFinalScore = (studentData) => {
  let totalScore = 0
  
  evaluationDimensions.forEach(dimension => {
    let dimensionWeightedScore = 0
    
    roleWeights.forEach(roleWeight => {
      const dimensionScores = studentData.scores[roleWeight.role][dimension.id]
      const dimensionAvg = calculateDimensionAverage(dimensionScores)
      dimensionWeightedScore += dimensionAvg * (roleWeight.weight / 100)
    })
    
    // 每个维度占总分的20%
    totalScore += dimensionWeightedScore * 0.2
  })
  
  return totalScore
}
```

## 📊 数据关系

### 1. 数据层次结构
```
评价系统
├── 评价维度 (5个)
│   └── 评价指标 (每个维度4个，共20个)
│       └── 评分标准 (每个指标5级标准)
└── 评价身份 (3个)
    └── 权重配置 (总和100%)
```

### 2. 评分计算流程
```
指标评分 (0-100) 
    ↓
维度平均分 = Σ(指标分数) / 4
    ↓
身份维度得分 = 维度平均分 × 身份权重
    ↓
最终成绩 = Σ(身份维度得分 × 维度权重20%)
```

## 🔧 维护指南

### 1. 添加新的评价指标

```javascript
// 在对应维度的indicators数组中添加
{
  id: 'm5',                    // 新指标ID
  name: '新指标名称',          // 指标名称
  criteria: '详细评分标准...'  // 评分标准
}
```

### 2. 修改权重配置

```javascript
// 修改roleWeights数组中的weight值
// 注意：所有权重之和应该等于100
{
  role: 'teacher',
  name: '教师评价',
  weight: 60,  // 从50修改为60
  icon: 'fa-book'
}
```

### 3. 添加新的评价维度

```javascript
// 在evaluationDimensions数组中添加新维度
{
  id: 'newDimension',
  name: '新维度名称',
  indicators: [
    // 添加4个指标
  ]
}
```

## 🔗 相关文件

- [`@/types/index.js`](../types/index.js) - 相关类型定义
- [`@/stores/evaluation.js`](../stores/evaluation.js) - 使用常量数据的状态管理
- [`@/views/EvaluationView.vue`](../views/EvaluationView.vue) - 评价页面组件

## 📝 开发规范

### 1. 数据格式规范
- **ID命名**: 使用简短的英文标识符
- **名称**: 使用中文描述性名称
- **评分标准**: 提供完整的5级评分描述

### 2. 权重配置规范
- **权重总和**: 所有身份权重之和必须等于100
- **维度权重**: 每个维度默认占总分的20%
- **权重调整**: 修改权重时需要同步更新相关计算逻辑

### 3. 扩展性考虑
- **向后兼容**: 新增数据时保持现有结构不变
- **数据验证**: 添加数据验证逻辑确保数据完整性
- **文档更新**: 修改数据结构时同步更新文档
