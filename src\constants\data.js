// 评价维度数据
export const evaluationDimensions = [
  {
    id: 'management',
    name: '管理力',
    indicators: [
      {
        id: 'm1',
        name: '全流程完整性',
        criteria:
          '80～100分：全流程无缝衔接，文化适配精准；61～80分：流程完整，协作略迟滞；41～60分：遗漏1环节，文化元素生硬；21～40分：流程断裂，无文化整合；0～20：未完成',
      },
      {
        id: 'm2',
        name: '跨岗位协作',
        criteria:
          '80～100分：全流程无缝衔接，文化适配精准；61～80分：流程完整，协作略迟滞；41～60分：遗漏1环节，文化元素生硬；21～40分：流程断裂，无文化整合；0～20：未完成',
      },
      {
        id: 'm3',
        name: '文化资源整合',
        criteria:
          '80～100分：全流程无缝衔接，文化适配精准；61～80分：流程完整，协作略迟滞；41～60分：遗漏1环节，文化元素生硬；21～40分：流程断裂，无文化整合；0～20：未完成',
      },
      {
        id: 'm4',
        name: '时效管理',
        criteria:
          '80～100分：全流程无缝衔接，文化适配精准；61～80分：流程完整，协作略迟滞；41～60分：遗漏1环节，文化元素生硬；21～40分：流程断裂，无文化整合；0～20：未完成',
      },
    ],
  },
  {
    id: 'execution',
    name: '执行力',
    indicators: [
      {
        id: 'e1',
        name: '动作标准化',
        criteria:
          '80～100分：动作精准高效，零失误；61～80分：操作规范，轻微延迟；41～60分：1处错误/超时；21～40分：多次错误/超时；0～20：引发安全隐患',
      },
      {
        id: 'e2',
        name: '设备使用',
        criteria:
          '80～100分：动作精准高效，零失误；61～80分：操作规范，轻微延迟；41～60分：1处错误/超时；21～40分：多次错误/超时；0～20：引发安全隐患',
      },
      {
        id: 'e3',
        name: '时效性',
        criteria:
          '80～100分：动作精准高效，零失误；61～80分：操作规范，轻微延迟；41～60分：1处错误/超时；21～40分：多次错误/超时；0～20：引发安全隐患',
      },
      {
        id: 'e4',
        name: '细节处理',
        criteria:
          '80～100分：动作精准高效，零失误；61～80分：操作规范，轻微延迟；41～60分：1处错误/超时；21～40分：多次错误/超时；0～20：引发安全隐患',
      },
    ],
  },
  {
    id: 'observation',
    name: '观察力',
    indicators: [
      {
        id: 'o1',
        name: '显性需求响应',
        criteria:
          '80～100分：隐性需求识别率≥90%；61～80分：显性需求全满足，隐性需求识别1处；41～60分：仅响应显性需求；21～40分：忽视关键行为信号；0～20分：需求误判',
      },
      {
        id: 'o2',
        name: '隐性需求洞察',
        criteria:
          '80～100分：隐性需求识别率≥90%；61～80分：显性需求全满足，隐性需求识别1处；41～60分：仅响应显性需求；21～40分：忽视关键行为信号；0～20分：需求误判',
      },
      {
        id: 'o3',
        name: '风险预判',
        criteria:
          '80～100分：隐性需求识别率≥90%；61～80分：显性需求全满足，隐性需求识别1处；41～60分：仅响应显性需求；21～40分：忽视关键行为信号；0～20分：需求误判',
      },
      {
        id: 'o4',
        name: '文化敏感度',
        criteria:
          '80～100分：隐性需求识别率≥90%；61～80分：显性需求全满足，隐性需求识别1处；41～60分：仅响应显性需求；21～40分：忽视关键行为信号；0～20分：需求误判',
      },
    ],
  },
  {
    id: 'communication',
    name: '沟通力',
    indicators: [
      {
        id: 'c1',
        name: '语言策略',
        criteria:
          '80～100分：方言破冰成功，无禁忌用语；61～80分：普通话沟通流畅，非语言到位；41～60分：沟通机械，偶有禁忌词；21～40分：语言不当引发不适；0～20分：沟通中断',
      },
      {
        id: 'c2',
        name: '非语言技巧',
        criteria:
          '80～100分：方言破冰成功，无禁忌用语；61～80分：普通话沟通流畅，非语言到位；41～60分：沟通机械，偶有禁忌词；21～40分：语言不当引发不适；0～20分：沟通中断',
      },
      {
        id: 'c3',
        name: '方言应用',
        criteria:
          '80～100分：方言破冰成功，无禁忌用语；61～80分：普通话沟通流畅，非语言到位；41～60分：沟通机械，偶有禁忌词；21～40分：语言不当引发不适；0～20分：沟通中断',
      },
      {
        id: 'c4',
        name: '禁忌规避',
        criteria:
          '80～100分：方言破冰成功，无禁忌用语；61～80分：普通话沟通流畅，非语言到位；41～60分：沟通机械，偶有禁忌词；21～40分：语言不当引发不适；0～20分：沟通中断',
      },
    ],
  },
  {
    id: 'adaptability',
    name: '应变力',
    indicators: [
      {
        id: 'a1',
        name: '流程异常处理',
        criteria:
          '80～100分：创新策略化解危机；61～80分：按预案处理，结果平稳；41～60分：处理延迟，旅客轻微不满；21～40分：依赖他人介入；0～20分：应对失当激化矛盾',
      },
      {
        id: 'a2',
        name: '冲突化解',
        criteria:
          '80～100分：创新策略化解危机；61～80分：按预案处理，结果平稳；41～60分：处理延迟，旅客轻微不满；21～40分：依赖他人介入；0～20分：应对失当激化矛盾',
      },
      {
        id: 'a3',
        name: '极端案例应对',
        criteria:
          '80～100分：创新策略化解危机；61～80分：按预案处理，结果平稳；41～60分：处理延迟，旅客轻微不满；21～40分：依赖他人介入；0～20分：应对失当激化矛盾',
      },
      {
        id: 'a4',
        name: '文化冲突调解',
        criteria:
          '80～100分：创新策略化解危机；61～80分：按预案处理，结果平稳；41～60分：处理延迟，旅客轻微不满；21～40分：依赖他人介入；0～20分：应对失当激化矛盾',
      },
    ],
  },
]

// 身份权重配置
export const roleWeights = [
  {
    role: 'student',
    name: '学生评价',
    weight: 10,
    icon: 'fa-graduation-cap',
  },
  {
    role: 'teacher',
    name: '教师评价',
    weight: 50,
    icon: 'fa-book',
  },
  {
    role: 'enterprise',
    name: '企业评价',
    weight: 40,
    icon: 'fa-building',
  },
]
