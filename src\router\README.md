# 路由模块 (Router)

该目录包含Vue Router的配置和路由管理，定义了应用的导航结构和页面跳转逻辑。

## 📁 文件结构

```
src/router/
├── index.js          # 路由配置文件
└── README.md         # 路由模块文档
```

## 🛣️ 路由架构

### 1. 路由模式
- 使用 **History模式** (`createWebHistory`)
- 支持现代浏览器的HTML5 History API
- 提供更友好的URL结构（无#号）

### 2. 懒加载设计
- 所有页面组件采用动态导入
- 支持代码分割和按需加载
- 提升应用初始加载性能

### 3. 路由守卫
- 全局前置守卫处理页面标题
- 统一的页面标题格式
- 支持扩展认证和权限控制

## 📄 路由配置详解

### `index.js` - 主路由配置

**功能**: 定义应用的完整路由结构和导航逻辑

#### 路由表结构

```javascript
const routes = [
  {
    path: '/',                    // 路由路径
    name: 'RoleSelection',        // 路由名称
    component: () => import('@/views/RoleSelectionView.vue'), // 懒加载组件
    meta: {
      title: '身份选择'           // 页面标题
    }
  }
  // ... 更多路由
]
```

#### 完整路由列表

| 路径 | 名称 | 组件 | 功能描述 |
|------|------|------|----------|
| `/` | RoleSelection | RoleSelectionView | 首页 - 选择评价身份 |
| `/student-selection` | StudentSelection | StudentSelectionView | 学生选择页面 |
| `/evaluation/:studentId` | Evaluation | EvaluationView | 评价打分页面 |
| `/score` | Score | ScoreView | 成绩展示页面 |

## 🔄 导航流程

### 1. 标准评价流程
```
首页 (/) 
    ↓ 选择身份
学生选择 (/student-selection)
    ↓ 选择学生
评价打分 (/evaluation/:studentId)
    ↓ 完成评价
成绩展示 (/score)
```

### 2. 路由参数
- **`:studentId`** - 学生ID参数，用于标识被评价的学生
- 支持动态路由匹配
- 在组件中通过 `$route.params.studentId` 访问

## 🚀 使用指南

### 1. 编程式导航

```javascript
import { useRouter, useRoute } from 'vue-router'

export default {
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 跳转到学生选择页面
    const goToStudentSelection = () => {
      router.push('/student-selection')
    }
    
    // 跳转到评价页面（带参数）
    const goToEvaluation = (studentId) => {
      router.push(`/evaluation/${studentId}`)
      // 或者使用命名路由
      router.push({
        name: 'Evaluation',
        params: { studentId }
      })
    }
    
    // 返回上一页
    const goBack = () => {
      router.back()
    }
    
    // 获取当前路由参数
    const currentStudentId = route.params.studentId
    
    return {
      goToStudentSelection,
      goToEvaluation,
      goBack,
      currentStudentId
    }
  }
}
```

### 2. 声明式导航

```vue
<template>
  <!-- 基础导航 -->
  <router-link to="/">返回首页</router-link>
  <router-link to="/student-selection">选择学生</router-link>
  
  <!-- 命名路由导航 -->
  <router-link :to="{ name: 'Score' }">查看成绩</router-link>
  
  <!-- 带参数导航 -->
  <router-link :to="`/evaluation/${studentId}`">
    开始评价
  </router-link>
  
  <!-- 使用命名路由和参数 -->
  <router-link :to="{ name: 'Evaluation', params: { studentId } }">
    评价学生
  </router-link>
</template>
```

### 3. 路由守卫使用

```javascript
// 在组件中使用路由守卫
export default {
  beforeRouteEnter(to, from, next) {
    // 进入路由前的逻辑
    console.log('即将进入:', to.name)
    next()
  },
  
  beforeRouteUpdate(to, from, next) {
    // 路由更新时的逻辑（如参数变化）
    console.log('路由更新:', to.params.studentId)
    next()
  },
  
  beforeRouteLeave(to, from, next) {
    // 离开路由前的逻辑
    const answer = window.confirm('确定要离开吗？未保存的数据将丢失。')
    if (answer) {
      next()
    } else {
      next(false)
    }
  }
}
```

### 4. 获取路由信息

```vue
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()
const router = useRouter()

// 获取路由参数
const studentId = computed(() => route.params.studentId)

// 获取查询参数
const role = computed(() => route.query.role)

// 获取路由元信息
const pageTitle = computed(() => route.meta.title)

// 监听路由变化
watch(() => route.params.studentId, (newId, oldId) => {
  console.log(`学生ID从 ${oldId} 变更为 ${newId}`)
  // 重新加载数据
  loadStudentData(newId)
})
</script>

<template>
  <div>
    <h1>{{ pageTitle }}</h1>
    <p v-if="studentId">当前评价学生ID: {{ studentId }}</p>
    <p v-if="role">当前身份: {{ role }}</p>
  </div>
</template>
```

## 🔒 路由守卫

### 1. 全局前置守卫

当前实现的功能：
```javascript
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 客舱设施与服务五力评价系统`
  } else {
    document.title = '客舱设施与服务五力评价系统'
  }
  next()
})
```

### 2. 扩展守卫功能

可以扩展的功能示例：

```javascript
// 认证守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 需要认证的路由
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  
  if (requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})

// 权限守卫
router.beforeEach((to, from, next) => {
  const userRole = getUserRole()
  const requiredRole = to.meta.role
  
  if (requiredRole && !hasPermission(userRole, requiredRole)) {
    next('/403') // 无权限页面
  } else {
    next()
  }
})
```

## 📱 响应式导航

### 1. 移动端适配

```vue
<template>
  <div class="navigation">
    <!-- 桌面端导航 -->
    <nav class="hidden md:block">
      <router-link to="/" class="nav-link">首页</router-link>
      <router-link to="/student-selection" class="nav-link">学生选择</router-link>
      <router-link to="/score" class="nav-link">成绩展示</router-link>
    </nav>
    
    <!-- 移动端导航 -->
    <nav class="md:hidden">
      <button @click="toggleMobileMenu" class="menu-button">
        <i class="fa fa-bars"></i>
      </button>
      <div v-show="showMobileMenu" class="mobile-menu">
        <router-link to="/" @click="closeMobileMenu">首页</router-link>
        <router-link to="/student-selection" @click="closeMobileMenu">学生选择</router-link>
        <router-link to="/score" @click="closeMobileMenu">成绩展示</router-link>
      </div>
    </nav>
  </div>
</template>
```

### 2. 面包屑导航

```vue
<template>
  <nav class="breadcrumb">
    <router-link to="/">首页</router-link>
    <span v-if="route.name === 'StudentSelection'"> > 学生选择</span>
    <span v-if="route.name === 'Evaluation'"> > 学生选择 > 评价打分</span>
    <span v-if="route.name === 'Score'"> > 成绩展示</span>
  </nav>
</template>

<script setup>
import { useRoute } from 'vue-router'
const route = useRoute()
</script>
```

## 🔧 路由配置扩展

### 1. 添加新路由

```javascript
// 在routes数组中添加新路由
{
  path: '/settings',
  name: 'Settings',
  component: () => import('@/views/SettingsView.vue'),
  meta: {
    title: '系统设置',
    requiresAuth: true,  // 需要认证
    role: 'admin'        // 需要管理员权限
  }
}
```

### 2. 嵌套路由

```javascript
{
  path: '/admin',
  name: 'Admin',
  component: () => import('@/views/AdminLayout.vue'),
  children: [
    {
      path: 'users',
      name: 'AdminUsers',
      component: () => import('@/views/admin/UsersView.vue'),
      meta: { title: '用户管理' }
    },
    {
      path: 'settings',
      name: 'AdminSettings',
      component: () => import('@/views/admin/SettingsView.vue'),
      meta: { title: '系统设置' }
    }
  ]
}
```

### 3. 路由别名和重定向

```javascript
{
  path: '/home',
  redirect: '/'  // 重定向到首页
},
{
  path: '/evaluation/:studentId',
  name: 'Evaluation',
  alias: '/eval/:studentId',  // 路由别名
  component: () => import('@/views/EvaluationView.vue')
}
```

## 🔗 相关文件

- [`src/main.js`](../main.js) - 路由实例挂载
- [`src/App.vue`](../App.vue) - 路由视图容器
- [`src/views/`](../views/) - 路由对应的页面组件
- [`src/components/AppHeader.vue`](../components/AppHeader.vue) - 导航组件

## 📝 开发规范

### 1. 路由命名规范
- **路径**: 使用kebab-case，如 `/student-selection`
- **名称**: 使用PascalCase，如 `StudentSelection`
- **组件**: 与路由名称保持一致

### 2. 路由参数规范
- **动态参数**: 使用描述性名称，如 `:studentId`
- **查询参数**: 用于可选的过滤和状态传递
- **参数验证**: 在组件中验证参数的有效性

### 3. 元信息规范
- **title**: 必须提供页面标题
- **requiresAuth**: 标识是否需要认证
- **role**: 标识所需的用户角色
- **keepAlive**: 标识是否需要缓存组件

### 4. 性能优化
- **懒加载**: 所有路由组件使用动态导入
- **预加载**: 对重要路由进行预加载
- **缓存**: 合理使用keep-alive缓存组件
