import request from '@/utils/request'

/**
 * 获取指标列表
 * @param {string} service - 服务名称
 * @param {Object} [options] - 可选参数
 * @param {number} [options.pageNum] - 页码，默认为1
 * @param {number} [options.pageSize] - 页面大小，默认为50（确保获取所有题目）
 * @returns {Promise<Object>} 指标列表响应
 */
export function getIndicatorList(service, options = {}) {
  const { pageNum = 1, pageSize = 50 } = options

  return request({
    url: '/hk/indicator/list',
    method: 'get',
    params: {
      service: service,
      pageNum: pageNum,
      pageSize: pageSize
    }
  })
}

/**
 * 获取维度列表
 * @returns {Promise<Object>} 维度列表响应
 */
export function getDimensionalityList() {
  return request({
    url: '/system/dict/data/type/hk_dimensionality',
    method: 'get'
  })
}
