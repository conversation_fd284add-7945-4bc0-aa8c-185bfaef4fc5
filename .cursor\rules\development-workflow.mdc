---
description: 
globs: 
alwaysApply: false
---
# 开发工作流和最佳实践

## Vue 3 开发模式
项目使用现代化的 Vue 3 语法和最佳实践：

### Composition API 模式
```vue
<script setup lang="ts">
// 使用 <script setup> 语法
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const selectedRole = ref<Role | null>(null)

// 计算属性
const isFormValid = computed(() => {
  return selectedRole.value !== null
})

// 生命周期
onMounted(() => {
  initializeData()
})
</script>
```

### TypeScript 最佳实践
- 所有组件必须使用 TypeScript
- 导入类型时使用 `import type` 语法
- 定义完整的 Props 和 Emits 类型
- 使用严格的类型检查

### Pinia 状态管理模式
在 [src/stores/evaluation.ts](mdc:src/stores/evaluation.ts) 中：
```typescript
export const useEvaluationStore = defineStore('evaluation', () => {
  // 状态定义
  const selectedRole = ref<Role | null>(null)
  
  // Getters (computed)
  const isEvaluationComplete = computed(() => {
    // 计算逻辑
  })
  
  // Actions (functions)
  const updateScore = (studentId, score) => {
    // 更新逻辑
  }
  
  return {
    selectedRole,
    isEvaluationComplete,
    updateScore
  }
})
```

## 组件开发规范

### 文件命名
- 页面组件：`PascalCase` + `View.vue` (如 `RoleSelectionView.vue`)
- 公共组件：`PascalCase` + `.vue` (如 `AppHeader.vue`)
- Store文件：`camelCase` + `.ts` (如 `evaluation.ts`)

### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, computed } from 'vue'
import type { Student } from '@/types'

// 2. 定义 props 和 emits
interface Props {
  student: Student
}
const props = defineProps<Props>()

// 3. 响应式数据
const isLoading = ref(false)

// 4. 计算属性
const displayName = computed(() => {
  return `${props.student.name} (${props.student.id})`
})

// 5. 方法定义
const handleSubmit = () => {
  // 处理逻辑
}

// 6. 生命周期钩子
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/* 组件专用样式 */
</style>
```

## 数据流模式

### 单向数据流
- 父组件通过 props 向子组件传递数据
- 子组件通过 emits 向父组件发送事件
- 全局状态通过 Pinia store 管理

### 数据持久化
- 评价数据自动保存到 localStorage
- 页面刷新后自动恢复状态
- 使用 `saveEvaluationData()` 函数统一保存

## 错误处理模式
```typescript
try {
  await updateStudentScore(studentId, score)
  showSuccess('保存成功')
} catch (error) {
  console.error('保存失败:', error)
  showError('保存失败，请重试')
}
```

## 性能优化要点
1. **懒加载**: 路由使用动态导入
2. **计算属性**: 复杂计算使用 computed
3. **防抖**: 搜索功能使用防抖
4. **虚拟滚动**: 大量数据时考虑虚拟滚动

## 测试策略
- 单元测试：关键业务逻辑函数
- 组件测试：用户交互场景
- E2E测试：完整的评价流程

## 部署注意事项
1. 确保所有依赖都在 package.json 中
2. 检查 TypeScript 编译无错误
3. 验证生产环境的 Tailwind CSS 构建
4. 测试在不同设备上的响应式表现

