import request from '@/utils/request'

/**
 * 登录
 * @param {import('@/types').LoginData} data - 登录数据
 * @returns {Promise<import('@/types').LoginResponse>} 登录响应
 */
export function login(data) {
  return request({
    url: '/login',
    method: 'post',
    data,
  })
}

/**
 * 获取用户信息
 * @returns {Promise<import('@/types').UserProfile>} 用户信息
 */
export function getProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get',
  })
}
